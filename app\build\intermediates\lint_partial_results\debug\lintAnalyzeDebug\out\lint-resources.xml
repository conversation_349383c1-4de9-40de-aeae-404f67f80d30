http://schemas.android.com/apk/res-auto;;${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/placeholder_album_art.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_repeat.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pause.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_skip_next.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_skip_previous.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_more_vert.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_repeat_one.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_equalizer.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_music_note.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play_arrow.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_shuffle.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/mini_player.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_song.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:colorPrimary,0,V400090154,2e0009017e,;"#E91E63";surface,0,V4001302d9,29001302fe,;"#2C2C2C";text_primary,0,V40017036f,2e00170399,;"#FFFFFF";black,0,V400020037,290002005c,;"#FF000000";text_tertiary,0,V4001903cf,2f001903fa,;"#808080";background_secondary,0,V4001202a2,36001202d4,;"#1E1E1E";error,0,V400200498,27002004bb,;"#F44336";accent,0,V4001f046f,28001f0493,;"#FF6B6B";progress_background,0,V4001c041d,35001c044e,;"#404040";colorAccent,0,V4000a0183,2d000a01ac,;"#FF6B6B";white,0,V400030061,**********,;"#FFFFFFFF";success,0,V4002104c0,29002104e5,;"#4CAF50";headphone_red,0,V4000e01fe,2f000e0229,;"#F44336";warning,0,V4002204ea,290022050f,;"#FF9800";background_primary,0,V40011026d,340011029d,;"#121212";surface_variant,0,V400140303,**********,;"#3C3C3C";primary_dark,0,V4000700f5,2e0007011f,;"#C2185B";text_secondary,0,V40018039e,30001803ca,;"#B3B3B3";primary,0,V4000600cb,29000600f0,;"#E91E63";primary_light,0,V400080124,2f0008014f,;"#F48FB1";headphone_pink,0,V4000d01cd,30000d01f9,;"#E91E63";+drawable:placeholder_album_art,1,F;ic_repeat,2,F;ic_pause,3,F;ic_skip_next,4,F;ic_arrow_back,5,F;ic_skip_previous,6,F;ic_more_vert,7,F;ic_launcher_foreground,8,F;ic_play,9,F;ic_launcher_background,10,F;ic_repeat_one,11,F;ic_equalizer,12,F;ic_search,13,F;ic_music_note,14,F;ic_play_arrow,15,F;ic_shuffle,16,F;+id:miniArtistName,17,F;songInfoLayout,18,F;progressLayout,18,F;tvCurrentTime,18,F;searchView,19,F;miniPreviousButton,17,F;searchCard,19,F;tvDuration,20,F;tvTitle,20,F;main,19,F;tvTotalTime,18,F;btnRepeat,18,F;tvArtistName,18,F;tvEmptyState,19,F;progressBar,19,F;progressBar,18,F;ivAlbumArt,18,F;ivAlbumArt,20,F;btnPrevious,18,F;miniPlayPauseButton,17,F;btnBack,18,F;ivPlayingIndicator,20,F;albumArtworkCard,18,F;btnShuffle,18,F;tvArtist,20,F;miniProgressBar,17,F;btnNext,18,F;swipeRefreshLayout,19,F;recyclerViewSongs,19,F;miniAlbumArtwork,17,F;miniNextButton,17,F;tvSongTitle,18,F;controlButtonsLayout,18,F;toolbar,19,F;miniSongTitle,17,F;btnPlayPause,18,F;seekBar,18,F;+layout:item_song,20,F;activity_main,19,F;mini_player,17,F;activity_player,18,F;+mipmap:ic_launcher_round,21,F;ic_launcher_round,22,F;ic_launcher_round,23,F;ic_launcher_round,24,F;ic_launcher_round,25,F;ic_launcher_round,26,F;ic_launcher,27,F;ic_launcher,28,F;ic_launcher,29,F;ic_launcher,30,F;ic_launcher,31,F;ic_launcher,32,F;+string:play,33,V4000a0184,25000a01a5,;"Play";next,33,V4000d0200,25000d0221,;"Next";album_artwork,33,V4001c048b,37001c04be,;"Album artwork";more_options,33,V40010027c,35001002ad,;"More options";previous,33,V4000c01d2,2d000c01fb,;"Previous";playing_indicator,33,V4001d04c3,3f001d04fe,;"Currently playing";search_hint,33,V40004005e,46000400a0,;"Search songs\, artists\, albums…";error_playback,33,V400190425,4200190463,;"Playback error occurred";notification_channel_description,33,V400140313,5800140367,;"Controls for music playback";pause,33,V4000b01aa,27000b01cd,;"Pause";error_network,33,V4001803ce,5600180420,;"Network error. Please check your connection.";app_name,33,V400010010,310001003d,;"Music Player";error_loading_songs,33,V400170389,44001703c9,;"Failed to load songs";try_refreshing,33,V4000600df,520006012d,;"Try refreshing or check your connection";now_playing,33,V400090150,330009017f,;"Now Playing";repeat,33,V4000f0252,29000f0277,;"Repeat";notification_channel_name,33,V4001302ce,440013030e,;"Music Playback";no_songs_found,33,V4000500a5,39000500da,;"No songs found";shuffle,33,V4000e0226,2b000e024d,;"Shuffle";+style:Base.Theme.MusicPlayer,34,V400020082,c00160429,;DTheme.Material3.Dark.NoActionBar,colorPrimary:@color/primary,colorPrimaryVariant:@color/primary_dark,colorOnPrimary:@color/white,colorSurface:@color/surface,colorOnSurface:@color/text_primary,android\:colorBackground:@color/background_primary,android\:statusBarColor:@color/background_primary,android\:windowLightStatusBar:false,android\:navigationBarColor:@color/background_primary,android\:windowBackground:@color/background_primary,;Base.Theme.MusicPlayer,35,V400020064,c0005013e,;DTheme.Material3.DayNight.NoActionBar,;Theme.MusicPlayer,34,V40018042f,4600180471,;DBase.Theme.MusicPlayer,;+xml:data_extraction_rules,36,F;backup_rules,37,F;