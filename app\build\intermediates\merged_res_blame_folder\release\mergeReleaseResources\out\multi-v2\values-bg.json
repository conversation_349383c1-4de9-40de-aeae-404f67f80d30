{"logs": [{"outputFile": "com.nauh.musicplayer.app-mergeReleaseResources-40:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8c6e034149c65399529a9cd0f184f8b\\transformed\\media3-session-1.2.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,133,227,295,364,448,536,639", "endColumns": "77,93,67,68,83,87,102,106", "endOffsets": "128,222,290,359,443,531,634,741"}, "to": {"startLines": "50,66,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3715,5324,10665,10733,10802,10886,10974,11077", "endColumns": "77,93,67,68,83,87,102,106", "endOffsets": "3788,5413,10728,10797,10881,10969,11072,11179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e355fbe8fe59cc5407000c654ab4b66\\transformed\\appcompat-1.6.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "966,1086,1192,1297,1383,1493,1614,1694,1771,1862,1955,2050,2144,2244,2337,2432,2540,2631,2722,2805,2919,3027,3127,3241,3348,3456,3616,14748", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "1081,1187,1292,1378,1488,1609,1689,1766,1857,1950,2045,2139,2239,2332,2427,2535,2626,2717,2800,2914,3022,3122,3236,3343,3451,3611,3710,14827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87c47c7abe7f29a54b096c7ff4d96851\\transformed\\core-1.16.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "56,57,58,59,60,61,62,185", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4266,4363,4473,4575,4676,4783,4888,14832", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "4358,4468,4570,4671,4778,4883,5002,14928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c077dfbc8dc20562e970eb684d92a281\\transformed\\media3-ui-1.2.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,500,706,796,887,970,1057,1145,1225,1290,1394,1499,1577,1651,1715,1783,1908,2029,2157,2234,2326,2398,2479,2580,2682,2748,2816,2870,2930,2978,3039,3111,3179,3242,3318,3383,3441,3512,3577,3648,3700,3759,3840,3921", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,89,90,82,86,87,79,64,103,104,77,73,63,67,124,120,127,76,91,71,80,100,101,65,67,53,59,47,60,71,67,62,75,64,57,70,64,70,51,58,80,80,56", "endOffsets": "282,495,701,791,882,965,1052,1140,1220,1285,1389,1494,1572,1646,1710,1778,1903,2024,2152,2229,2321,2393,2474,2575,2677,2743,2811,2865,2925,2973,3034,3106,3174,3237,3313,3378,3436,3507,3572,3643,3695,3754,3835,3916,3973"}, "to": {"startLines": "2,11,15,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,595,5484,5574,5665,5748,5835,5923,6003,6068,6172,6277,6355,6429,6493,6561,6686,6807,6935,7012,7104,7176,7257,7358,7460,7526,8281,8335,8395,8443,8504,8576,8644,8707,8783,8848,8906,8977,9042,9113,9165,9224,9305,9386", "endLines": "10,14,18,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "endColumns": "17,12,12,89,90,82,86,87,79,64,103,104,77,73,63,67,124,120,127,76,91,71,80,100,101,65,67,53,59,47,60,71,67,62,75,64,57,70,64,70,51,58,80,80,56", "endOffsets": "377,590,796,5569,5660,5743,5830,5918,5998,6063,6167,6272,6350,6424,6488,6556,6681,6802,6930,7007,7099,7171,7252,7353,7455,7521,7589,8330,8390,8438,8499,8571,8639,8702,8778,8843,8901,8972,9037,9108,9160,9219,9300,9381,9438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d41d36b14e47c257e85459df73d97ebd\\transformed\\media3-exoplayer-1.2.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,189,253,327,405,478,575,666", "endColumns": "70,62,63,73,77,72,96,90,75", "endOffsets": "121,184,248,322,400,473,570,661,737"}, "to": {"startLines": "92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7594,7665,7728,7792,7866,7944,8017,8114,8205", "endColumns": "70,62,63,73,77,72,96,90,75", "endOffsets": "7660,7723,7787,7861,7939,8012,8109,8200,8276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c6ed593d095e40b444f65ff3ae72d38a\\transformed\\material-1.10.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,423,506,628,738,833,966,1055,1121,1218,1298,1360,1449,1512,1577,1636,1709,1772,1826,1954,2011,2073,2127,2200,2343,2427,2515,2651,2739,2827,2963,3048,3125,3178,3229,3295,3370,3446,3532,3611,3688,3764,3841,3915,4027,4118,4193,4284,4376,4450,4537,4628,4683,4765,4831,4914,5000,5062,5126,5189,5259,5376,5488,5599,5709,5766,5821", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,78,82,121,109,94,132,88,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,87,135,87,87,135,84,76,52,50,65,74,75,85,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85", "endOffsets": "260,339,418,501,623,733,828,961,1050,1116,1213,1293,1355,1444,1507,1572,1631,1704,1767,1821,1949,2006,2068,2122,2195,2338,2422,2510,2646,2734,2822,2958,3043,3120,3173,3224,3290,3365,3441,3527,3606,3683,3759,3836,3910,4022,4113,4188,4279,4371,4445,4532,4623,4678,4760,4826,4909,4995,5057,5121,5184,5254,5371,5483,5594,5704,5761,5816,5902"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,67,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "801,3793,3872,3951,4034,4156,5007,5102,5235,5418,9443,9540,9620,9682,9771,9834,9899,9958,10031,10094,10148,10276,10333,10395,10449,10522,11184,11268,11356,11492,11580,11668,11804,11889,11966,12019,12070,12136,12211,12287,12373,12452,12529,12605,12682,12756,12868,12959,13034,13125,13217,13291,13378,13469,13524,13606,13672,13755,13841,13903,13967,14030,14100,14217,14329,14440,14550,14607,14662", "endLines": "22,51,52,53,54,55,63,64,65,67,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "endColumns": "12,78,78,82,121,109,94,132,88,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,87,135,87,87,135,84,76,52,50,65,74,75,85,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85", "endOffsets": "961,3867,3946,4029,4151,4261,5097,5230,5319,5479,9535,9615,9677,9766,9829,9894,9953,10026,10089,10143,10271,10328,10390,10444,10517,10660,11263,11351,11487,11575,11663,11799,11884,11961,12014,12065,12131,12206,12282,12368,12447,12524,12600,12677,12751,12863,12954,13029,13120,13212,13286,13373,13464,13519,13601,13667,13750,13836,13898,13962,14025,14095,14212,14324,14435,14545,14602,14657,14743"}}]}]}