  ExampleUnitTest com.nauh.musicplayer  Song com.nauh.musicplayer  
SongModelTest com.nauh.musicplayer  Test com.nauh.musicplayer  assertEquals com.nauh.musicplayer  assertEquals $com.nauh.musicplayer.ExampleUnitTest  Song "com.nauh.musicplayer.SongModelTest  assertEquals "com.nauh.musicplayer.SongModelTest  Song com.nauh.musicplayer.data.model  getArtistAlbumText $com.nauh.musicplayer.data.model.Song  getFormattedDuration $com.nauh.musicplayer.data.model.Song  plus 
kotlin.Int  Song 	org.junit  Test 	org.junit  assertEquals 	org.junit  assertEquals org.junit.Assert  
getMediaId $com.nauh.musicplayer.data.model.Song  Before com.nauh.musicplayer  Boolean com.nauh.musicplayer  Int com.nauh.musicplayer  List com.nauh.musicplayer  Long com.nauh.musicplayer  PlayerContract com.nauh.musicplayer  PlayerPresenter com.nauh.musicplayer  PlayerPresenterTest com.nauh.musicplayer  String com.nauh.musicplayer  
assertTrue com.nauh.musicplayer  listOf com.nauh.musicplayer  
mutableListOf com.nauh.musicplayer  
viewCallbacks com.nauh.musicplayer  View #com.nauh.musicplayer.PlayerContract  PlayerContract (com.nauh.musicplayer.PlayerPresenterTest  PlayerPresenter (com.nauh.musicplayer.PlayerPresenterTest  Song (com.nauh.musicplayer.PlayerPresenterTest  
assertTrue (com.nauh.musicplayer.PlayerPresenterTest  listOf (com.nauh.musicplayer.PlayerPresenterTest  mockView (com.nauh.musicplayer.PlayerPresenterTest  
mutableListOf (com.nauh.musicplayer.PlayerPresenterTest  	presenter (com.nauh.musicplayer.PlayerPresenterTest  testSong (com.nauh.musicplayer.PlayerPresenterTest  
viewCallbacks (com.nauh.musicplayer.PlayerPresenterTest  PlayerContract com.nauh.musicplayer.contract  
RepeatMode ,com.nauh.musicplayer.contract.PlayerContract  View ,com.nauh.musicplayer.contract.PlayerContract  ALL 7com.nauh.musicplayer.contract.PlayerContract.RepeatMode  OFF 7com.nauh.musicplayer.contract.PlayerContract.RepeatMode  ONE 7com.nauh.musicplayer.contract.PlayerContract.RepeatMode  title $com.nauh.musicplayer.data.model.Song  PlayerPresenter com.nauh.musicplayer.presenter  
attachView .com.nauh.musicplayer.presenter.PlayerPresenter  onPlaybackStateChanged .com.nauh.musicplayer.presenter.PlayerPresenter  onProgressUpdate .com.nauh.musicplayer.presenter.PlayerPresenter  
onSongChanged .com.nauh.musicplayer.presenter.PlayerPresenter  toggleRepeat .com.nauh.musicplayer.presenter.PlayerPresenter  
toggleShuffle .com.nauh.musicplayer.presenter.PlayerPresenter  List kotlin.collections  MutableList kotlin.collections  listOf kotlin.collections  
mutableListOf kotlin.collections  size kotlin.collections.List  add kotlin.collections.MutableList  clear kotlin.collections.MutableList  contains kotlin.collections.MutableList  Before 	org.junit  Boolean 	org.junit  Int 	org.junit  List 	org.junit  Long 	org.junit  PlayerContract 	org.junit  PlayerPresenter 	org.junit  String 	org.junit  
assertTrue 	org.junit  listOf 	org.junit  
mutableListOf 	org.junit  
viewCallbacks 	org.junit  
assertTrue org.junit.Assert  View org.junit.PlayerContract                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   