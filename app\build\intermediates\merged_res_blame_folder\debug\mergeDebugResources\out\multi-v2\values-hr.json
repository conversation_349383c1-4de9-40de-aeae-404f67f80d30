{"logs": [{"outputFile": "com.nauh.musicplayer.app-mergeDebugResources-40:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e355fbe8fe59cc5407000c654ab4b66\\transformed\\appcompat-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1179,1284,1379,1486,1572,1676,1795,1880,1962,2053,2146,2241,2335,2435,2528,2623,2718,2809,2900,2986,3090,3202,3303,3408,3522,3624,3793,14749", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "1279,1374,1481,1567,1671,1790,1875,1957,2048,2141,2236,2330,2430,2523,2618,2713,2804,2895,2981,3085,3197,3298,3403,3517,3619,3788,3885,14829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8c6e034149c65399529a9cd0f184f8b\\transformed\\media3-session-1.2.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,135,210,281,355,434,523,615", "endColumns": "79,74,70,73,78,88,91,96", "endOffsets": "130,205,276,350,429,518,610,707"}, "to": {"startLines": "53,69,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3890,5437,10697,10768,10842,10921,11010,11102", "endColumns": "79,74,70,73,78,88,91,96", "endOffsets": "3965,5507,10763,10837,10916,11005,11097,11194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87c47c7abe7f29a54b096c7ff4d96851\\transformed\\core-1.16.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "59,60,61,62,63,64,65,188", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4401,4499,4606,4703,4802,4906,5010,14834", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "4494,4601,4698,4797,4901,5005,5122,14930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d41d36b14e47c257e85459df73d97ebd\\transformed\\media3-exoplayer-1.2.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,566,648", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "125,186,251,324,403,476,561,643,716"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7627,7702,7763,7828,7901,7980,8053,8138,8220", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "7697,7758,7823,7896,7975,8048,8133,8215,8288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c077dfbc8dc20562e970eb684d92a281\\transformed\\media3-ui-1.2.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,864,945,1027,1107,1214,1321,1391,1458,1549,1641,1706,1777,1840,1912,2031,2155,2276,2344,2428,2499,2570,2674,2779,2846,2911,2964,3022,3070,3131,3205,3284,3360,3434,3498,3557,3628,3693,3764,3816,3879,3964,4049", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "290,580,859,940,1022,1102,1209,1316,1386,1453,1544,1636,1701,1772,1835,1907,2026,2150,2271,2339,2423,2494,2565,2669,2774,2841,2906,2959,3017,3065,3126,3200,3279,3355,3429,3493,3552,3623,3688,3759,3811,3874,3959,4044,4100"}, "to": {"startLines": "2,11,16,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,390,680,5580,5661,5743,5823,5930,6037,6107,6174,6265,6357,6422,6493,6556,6628,6747,6871,6992,7060,7144,7215,7286,7390,7495,7562,8293,8346,8404,8452,8513,8587,8666,8742,8816,8880,8939,9010,9075,9146,9198,9261,9346,9431", "endLines": "10,15,20,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "385,675,954,5656,5738,5818,5925,6032,6102,6169,6260,6352,6417,6488,6551,6623,6742,6866,6987,7055,7139,7210,7281,7385,7490,7557,7622,8341,8399,8447,8508,8582,8661,8737,8811,8875,8934,9005,9070,9141,9193,9256,9341,9426,9482"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c6ed593d095e40b444f65ff3ae72d38a\\transformed\\material-1.10.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1129,1225,1293,1356,1464,1524,1590,1646,1717,1777,1831,1957,2014,2076,2130,2205,2339,2424,2505,2642,2726,2812,2945,3036,3114,3170,3225,3291,3365,3443,3531,3613,3685,3762,3842,3916,4023,4116,4189,4281,4377,4451,4527,4623,4675,4757,4824,4911,4998,5060,5124,5187,5257,5363,5479,5576,5690,5750,5809", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,132,90,77,55,54,65,73,77,87,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79", "endOffsets": "315,393,471,556,653,746,842,972,1056,1124,1220,1288,1351,1459,1519,1585,1641,1712,1772,1826,1952,2009,2071,2125,2200,2334,2419,2500,2637,2721,2807,2940,3031,3109,3165,3220,3286,3360,3438,3526,3608,3680,3757,3837,3911,4018,4111,4184,4276,4372,4446,4522,4618,4670,4752,4819,4906,4993,5055,5119,5182,5252,5358,5474,5571,5685,5745,5804,5884"}, "to": {"startLines": "21,54,55,56,57,58,66,67,68,70,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "959,3970,4048,4126,4211,4308,5127,5223,5353,5512,9487,9583,9651,9714,9822,9882,9948,10004,10075,10135,10189,10315,10372,10434,10488,10563,11199,11284,11365,11502,11586,11672,11805,11896,11974,12030,12085,12151,12225,12303,12391,12473,12545,12622,12702,12776,12883,12976,13049,13141,13237,13311,13387,13483,13535,13617,13684,13771,13858,13920,13984,14047,14117,14223,14339,14436,14550,14610,14669", "endLines": "25,54,55,56,57,58,66,67,68,70,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,132,90,77,55,54,65,73,77,87,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79", "endOffsets": "1174,4043,4121,4206,4303,4396,5218,5348,5432,5575,9578,9646,9709,9817,9877,9943,9999,10070,10130,10184,10310,10367,10429,10483,10558,10692,11279,11360,11497,11581,11667,11800,11891,11969,12025,12080,12146,12220,12298,12386,12468,12540,12617,12697,12771,12878,12971,13044,13136,13232,13306,13382,13478,13530,13612,13679,13766,13853,13915,13979,14042,14112,14218,14334,14431,14545,14605,14664,14744"}}]}]}