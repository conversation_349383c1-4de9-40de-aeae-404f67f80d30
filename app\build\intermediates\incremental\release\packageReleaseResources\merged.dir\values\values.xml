<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#FF6B6B</color>
    <color name="background_primary">#F5F5F5</color>
    <color name="background_secondary">#FFFFFF</color>
    <color name="black">#FF000000</color>
    <color name="error">#F44336</color>
    <color name="headphone_pink">#E91E63</color>
    <color name="headphone_red">#F44336</color>
    <color name="primary">#4CAF50</color>
    <color name="primary_dark">#388E3C</color>
    <color name="primary_light">#81C784</color>
    <color name="progress_background">#404040</color>
    <color name="success">#4CAF50</color>
    <color name="surface">#FFFFFF</color>
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="text_tertiary">#9E9E9E</color>
    <color name="warning">#FF9800</color>
    <color name="white">#FFFFFFFF</color>
    <string name="album_artwork">Album artwork</string>
    <string name="app_name">Music Player</string>
    <string name="error_loading_songs">Failed to load songs</string>
    <string name="error_network">Network error. Please check your connection.</string>
    <string name="error_playback">Playback error occurred</string>
    <string name="more_options">More options</string>
    <string name="next">Next</string>
    <string name="no_songs_found">No songs found</string>
    <string name="notification_channel_description">Controls for music playback</string>
    <string name="notification_channel_name">Music Playback</string>
    <string name="now_playing">Now Playing</string>
    <string name="pause">Pause</string>
    <string name="play">Play</string>
    <string name="playing_indicator">Currently playing</string>
    <string name="previous">Previous</string>
    <string name="repeat">Repeat</string>
    <string name="search_hint">Search songs, artists, albums…</string>
    <string name="shuffle">Shuffle</string>
    <string name="try_refreshing">Try refreshing or check your connection</string>
    <style name="Base.Theme.MusicPlayer" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>

        
        <item name="android:colorBackground">@color/background_primary</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/text_primary</item>

        
        <item name="android:statusBarColor">@color/primary</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">@color/background_primary</item>
    </style>
    <style name="Theme.MusicPlayer" parent="Base.Theme.MusicPlayer"/>
</resources>