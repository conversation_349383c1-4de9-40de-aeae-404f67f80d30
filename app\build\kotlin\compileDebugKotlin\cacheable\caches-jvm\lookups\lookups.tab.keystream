  black android.<PERSON><PERSON>color  darker_gray android.R.color  Application android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  	ArrayList android.app.Activity  Boolean android.app.Activity  EXTRA_PLAYLIST android.app.Activity  
EXTRA_SONG android.app.Activity  Glide android.app.Activity  Int android.app.Activity  Intent android.app.Activity  LinearLayoutManager android.app.Activity  List android.app.Activity  
MainPresenter android.app.Activity  MusicRepository android.app.Activity  PlayerActivity android.app.Activity  PlayerContract android.app.Activity  PlayerPresenter android.app.Activity  R android.app.Activity  RequestOptions android.app.Activity  RoundedCorners android.app.Activity  
SearchView android.app.Activity  SeekBar android.app.Activity  Song android.app.Activity  SongAdapter android.app.Activity  String android.app.Activity  Toast android.app.Activity  View android.app.Activity  apply android.app.Activity  currentTime android.app.Activity  	emptyList android.app.Activity  finish android.app.Activity  format android.app.Activity  
formatTime android.app.Activity  intent android.app.Activity  
isNullOrBlank android.app.Activity  
isUserSeeking android.app.Activity  java android.app.Activity  let android.app.Activity  onCreate android.app.Activity  	presenter android.app.Activity  songAdapter android.app.Activity  
startActivity android.app.Activity  OnQueryTextListener android.app.Activity.SearchView  OnSeekBarChangeListener android.app.Activity.SeekBar  Log android.app.Application  TAG android.app.Application  onCreate android.app.Application  VISIBILITY_PUBLIC android.app.Notification  CHANNEL_DESCRIPTION android.app.NotificationChannel  Notification android.app.NotificationChannel  apply android.app.NotificationChannel  description android.app.NotificationChannel  lockscreenVisibility android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  IMPORTANCE_LOW android.app.NotificationManager  cancel android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  notify android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  
getService android.app.PendingIntent  ACTION_NEXT android.app.Service  ACTION_PAUSE android.app.Service  ACTION_PLAY android.app.Service  ACTION_PREVIOUS android.app.Service  AudioAttributes android.app.Service  Boolean android.app.Service  C android.app.Service  	ExoPlayer android.app.Service  Int android.app.Service  Intent android.app.Service  Log android.app.Service  MainActivity android.app.Service  	MediaItem android.app.Service  
MediaMetadata android.app.Service  MediaSession android.app.Service  NotificationHelper android.app.Service  
PendingIntent android.app.Service  Player android.app.Service  START_STICKY android.app.Service  TAG android.app.Service  android android.app.Service  java android.app.Service  let android.app.Service  mediaSession android.app.Service  run android.app.Service  updateNotification android.app.Service  Listener android.app.Service.Player  
ComponentName android.content  Context android.content  Intent android.content  ACTION_NEXT android.content.Context  ACTION_PAUSE android.content.Context  ACTION_PLAY android.content.Context  ACTION_PREVIOUS android.content.Context  	ArrayList android.content.Context  AudioAttributes android.content.Context  Boolean android.content.Context  C android.content.Context  EXTRA_PLAYLIST android.content.Context  
EXTRA_SONG android.content.Context  	ExoPlayer android.content.Context  Glide android.content.Context  Int android.content.Context  Intent android.content.Context  LinearLayoutManager android.content.Context  List android.content.Context  Log android.content.Context  MainActivity android.content.Context  
MainPresenter android.content.Context  	MediaItem android.content.Context  
MediaMetadata android.content.Context  MediaSession android.content.Context  MusicRepository android.content.Context  NOTIFICATION_SERVICE android.content.Context  NotificationHelper android.content.Context  
PendingIntent android.content.Context  Player android.content.Context  PlayerActivity android.content.Context  PlayerContract android.content.Context  PlayerPresenter android.content.Context  R android.content.Context  RequestOptions android.content.Context  RoundedCorners android.content.Context  START_STICKY android.content.Context  
SearchView android.content.Context  SeekBar android.content.Context  Song android.content.Context  SongAdapter android.content.Context  String android.content.Context  TAG android.content.Context  Toast android.content.Context  View android.content.Context  android android.content.Context  apply android.content.Context  currentTime android.content.Context  	emptyList android.content.Context  format android.content.Context  
formatTime android.content.Context  getColor android.content.Context  getSystemService android.content.Context  
isNullOrBlank android.content.Context  
isUserSeeking android.content.Context  java android.content.Context  let android.content.Context  mainExecutor android.content.Context  mediaSession android.content.Context  	presenter android.content.Context  run android.content.Context  songAdapter android.content.Context  startService android.content.Context  updateNotification android.content.Context  Listener android.content.Context.Player  OnQueryTextListener "android.content.Context.SearchView  OnSeekBarChangeListener android.content.Context.SeekBar  ACTION_NEXT android.content.ContextWrapper  ACTION_PAUSE android.content.ContextWrapper  ACTION_PLAY android.content.ContextWrapper  ACTION_PREVIOUS android.content.ContextWrapper  	ArrayList android.content.ContextWrapper  AudioAttributes android.content.ContextWrapper  Boolean android.content.ContextWrapper  C android.content.ContextWrapper  EXTRA_PLAYLIST android.content.ContextWrapper  
EXTRA_SONG android.content.ContextWrapper  	ExoPlayer android.content.ContextWrapper  Glide android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  LinearLayoutManager android.content.ContextWrapper  List android.content.ContextWrapper  Log android.content.ContextWrapper  MainActivity android.content.ContextWrapper  
MainPresenter android.content.ContextWrapper  	MediaItem android.content.ContextWrapper  
MediaMetadata android.content.ContextWrapper  MediaSession android.content.ContextWrapper  MusicRepository android.content.ContextWrapper  NotificationHelper android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  Player android.content.ContextWrapper  PlayerActivity android.content.ContextWrapper  PlayerContract android.content.ContextWrapper  PlayerPresenter android.content.ContextWrapper  R android.content.ContextWrapper  RequestOptions android.content.ContextWrapper  RoundedCorners android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  
SearchView android.content.ContextWrapper  SeekBar android.content.ContextWrapper  Song android.content.ContextWrapper  SongAdapter android.content.ContextWrapper  String android.content.ContextWrapper  TAG android.content.ContextWrapper  Toast android.content.ContextWrapper  View android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  currentTime android.content.ContextWrapper  	emptyList android.content.ContextWrapper  format android.content.ContextWrapper  
formatTime android.content.ContextWrapper  
isNullOrBlank android.content.ContextWrapper  
isUserSeeking android.content.ContextWrapper  java android.content.ContextWrapper  let android.content.ContextWrapper  mediaSession android.content.ContextWrapper  	presenter android.content.ContextWrapper  run android.content.ContextWrapper  songAdapter android.content.ContextWrapper  updateNotification android.content.ContextWrapper  Listener %android.content.ContextWrapper.Player  OnQueryTextListener )android.content.ContextWrapper.SearchView  OnSeekBarChangeListener &android.content.ContextWrapper.SeekBar  	ArrayList android.content.Intent  PlayerActivity android.content.Intent  action android.content.Intent  apply android.content.Intent  getParcelableArrayListExtra android.content.Intent  getParcelableExtra android.content.Intent  putExtra android.content.Intent  putParcelableArrayListExtra android.content.Intent  Bitmap android.graphics  Drawable android.graphics.drawable  Uri android.net  parse android.net.Uri  toString android.net.Uri  Build 
android.os  Bundle 
android.os  Handler 
android.os  Looper 
android.os  
Parcelable 
android.os  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  post android.os.Handler  postDelayed android.os.Handler  removeCallbacks android.os.Handler  
getMainLooper android.os.Looper  MediaSessionCompat  android.support.v4.media.session  Log android.util  d android.util.Log  e android.util.Log  LayoutInflater android.view  View android.view  	ViewGroup android.view  	ArrayList  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  EXTRA_PLAYLIST  android.view.ContextThemeWrapper  
EXTRA_SONG  android.view.ContextThemeWrapper  Glide  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  LinearLayoutManager  android.view.ContextThemeWrapper  List  android.view.ContextThemeWrapper  
MainPresenter  android.view.ContextThemeWrapper  MusicRepository  android.view.ContextThemeWrapper  PlayerActivity  android.view.ContextThemeWrapper  PlayerContract  android.view.ContextThemeWrapper  PlayerPresenter  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  RequestOptions  android.view.ContextThemeWrapper  RoundedCorners  android.view.ContextThemeWrapper  
SearchView  android.view.ContextThemeWrapper  SeekBar  android.view.ContextThemeWrapper  Song  android.view.ContextThemeWrapper  SongAdapter  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  currentTime  android.view.ContextThemeWrapper  	emptyList  android.view.ContextThemeWrapper  format  android.view.ContextThemeWrapper  
formatTime  android.view.ContextThemeWrapper  
isNullOrBlank  android.view.ContextThemeWrapper  
isUserSeeking  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  	presenter  android.view.ContextThemeWrapper  songAdapter  android.view.ContextThemeWrapper  OnQueryTextListener +android.view.ContextThemeWrapper.SearchView  OnSeekBarChangeListener (android.view.ContextThemeWrapper.SeekBar  from android.view.LayoutInflater  inflate android.view.LayoutInflater  GONE android.view.View  OnClickListener android.view.View  VISIBLE android.view.View  alpha android.view.View  context android.view.View  findViewById android.view.View  	isEnabled android.view.View  setOnClickListener android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  context android.view.ViewGroup  ImageButton android.widget  	ImageView android.widget  ProgressBar android.widget  SeekBar android.widget  TextView android.widget  Toast android.widget  alpha android.widget.ImageButton  	isEnabled android.widget.ImageButton  setImageResource android.widget.ImageButton  setOnClickListener android.widget.ImageButton  setImageResource android.widget.ImageView  
visibility android.widget.ImageView  max android.widget.ProgressBar  progress android.widget.ProgressBar  
visibility android.widget.ProgressBar  OnSeekBarChangeListener android.widget.SeekBar  let android.widget.SeekBar  max android.widget.SeekBar  progress android.widget.SeekBar  setOnSeekBarChangeListener android.widget.SeekBar  setTextColor android.widget.TextView  text android.widget.TextView  
visibility android.widget.TextView  LENGTH_LONG android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  	ArrayList #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  EXTRA_PLAYLIST #androidx.activity.ComponentActivity  
EXTRA_SONG #androidx.activity.ComponentActivity  Glide #androidx.activity.ComponentActivity  ImageButton #androidx.activity.ComponentActivity  	ImageView #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  LinearLayoutManager #androidx.activity.ComponentActivity  List #androidx.activity.ComponentActivity  Long #androidx.activity.ComponentActivity  
MainPresenter #androidx.activity.ComponentActivity  MusicRepository #androidx.activity.ComponentActivity  PlayerActivity #androidx.activity.ComponentActivity  PlayerContract #androidx.activity.ComponentActivity  PlayerPresenter #androidx.activity.ComponentActivity  ProgressBar #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  RecyclerView #androidx.activity.ComponentActivity  RequestOptions #androidx.activity.ComponentActivity  RoundedCorners #androidx.activity.ComponentActivity  
SearchView #androidx.activity.ComponentActivity  SeekBar #androidx.activity.ComponentActivity  Song #androidx.activity.ComponentActivity  SongAdapter #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  SwipeRefreshLayout #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  currentTime #androidx.activity.ComponentActivity  	emptyList #androidx.activity.ComponentActivity  format #androidx.activity.ComponentActivity  
formatTime #androidx.activity.ComponentActivity  
isNullOrBlank #androidx.activity.ComponentActivity  
isUserSeeking #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  	presenter #androidx.activity.ComponentActivity  songAdapter #androidx.activity.ComponentActivity  	ArrayList -androidx.activity.ComponentActivity.Companion  EXTRA_PLAYLIST -androidx.activity.ComponentActivity.Companion  
EXTRA_SONG -androidx.activity.ComponentActivity.Companion  Glide -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  LinearLayoutManager -androidx.activity.ComponentActivity.Companion  
MainPresenter -androidx.activity.ComponentActivity.Companion  MusicRepository -androidx.activity.ComponentActivity.Companion  PlayerActivity -androidx.activity.ComponentActivity.Companion  PlayerContract -androidx.activity.ComponentActivity.Companion  PlayerPresenter -androidx.activity.ComponentActivity.Companion  R -androidx.activity.ComponentActivity.Companion  RequestOptions -androidx.activity.ComponentActivity.Companion  RoundedCorners -androidx.activity.ComponentActivity.Companion  SongAdapter -androidx.activity.ComponentActivity.Companion  String -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  View -androidx.activity.ComponentActivity.Companion  apply -androidx.activity.ComponentActivity.Companion  currentTime -androidx.activity.ComponentActivity.Companion  	emptyList -androidx.activity.ComponentActivity.Companion  format -androidx.activity.ComponentActivity.Companion  
formatTime -androidx.activity.ComponentActivity.Companion  
isNullOrBlank -androidx.activity.ComponentActivity.Companion  
isUserSeeking -androidx.activity.ComponentActivity.Companion  java -androidx.activity.ComponentActivity.Companion  let -androidx.activity.ComponentActivity.Companion  	presenter -androidx.activity.ComponentActivity.Companion  songAdapter -androidx.activity.ComponentActivity.Companion  OnQueryTextListener .androidx.activity.ComponentActivity.SearchView  OnSeekBarChangeListener +androidx.activity.ComponentActivity.SeekBar  AppCompatActivity androidx.appcompat.app  	ArrayList (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  EXTRA_PLAYLIST (androidx.appcompat.app.AppCompatActivity  
EXTRA_SONG (androidx.appcompat.app.AppCompatActivity  Glide (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  LinearLayoutManager (androidx.appcompat.app.AppCompatActivity  List (androidx.appcompat.app.AppCompatActivity  
MainPresenter (androidx.appcompat.app.AppCompatActivity  MusicRepository (androidx.appcompat.app.AppCompatActivity  PlayerActivity (androidx.appcompat.app.AppCompatActivity  PlayerContract (androidx.appcompat.app.AppCompatActivity  PlayerPresenter (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  RequestOptions (androidx.appcompat.app.AppCompatActivity  RoundedCorners (androidx.appcompat.app.AppCompatActivity  
SearchView (androidx.appcompat.app.AppCompatActivity  SeekBar (androidx.appcompat.app.AppCompatActivity  Song (androidx.appcompat.app.AppCompatActivity  SongAdapter (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  currentTime (androidx.appcompat.app.AppCompatActivity  	emptyList (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  format (androidx.appcompat.app.AppCompatActivity  
formatTime (androidx.appcompat.app.AppCompatActivity  
isNullOrBlank (androidx.appcompat.app.AppCompatActivity  
isUserSeeking (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  onResume (androidx.appcompat.app.AppCompatActivity  	presenter (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  songAdapter (androidx.appcompat.app.AppCompatActivity  OnQueryTextListener 3androidx.appcompat.app.AppCompatActivity.SearchView  OnSeekBarChangeListener 0androidx.appcompat.app.AppCompatActivity.SeekBar  
SearchView androidx.appcompat.widget  OnQueryTextListener $androidx.appcompat.widget.SearchView  setOnQueryTextListener $androidx.appcompat.widget.SearchView  NotificationCompat androidx.core.app  	ArrayList #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  EXTRA_PLAYLIST #androidx.core.app.ComponentActivity  
EXTRA_SONG #androidx.core.app.ComponentActivity  Glide #androidx.core.app.ComponentActivity  ImageButton #androidx.core.app.ComponentActivity  	ImageView #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  LinearLayoutManager #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  Long #androidx.core.app.ComponentActivity  
MainPresenter #androidx.core.app.ComponentActivity  MusicRepository #androidx.core.app.ComponentActivity  PlayerActivity #androidx.core.app.ComponentActivity  PlayerContract #androidx.core.app.ComponentActivity  PlayerPresenter #androidx.core.app.ComponentActivity  ProgressBar #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  RecyclerView #androidx.core.app.ComponentActivity  RequestOptions #androidx.core.app.ComponentActivity  RoundedCorners #androidx.core.app.ComponentActivity  
SearchView #androidx.core.app.ComponentActivity  SeekBar #androidx.core.app.ComponentActivity  Song #androidx.core.app.ComponentActivity  SongAdapter #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  SwipeRefreshLayout #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  currentTime #androidx.core.app.ComponentActivity  	emptyList #androidx.core.app.ComponentActivity  format #androidx.core.app.ComponentActivity  
formatTime #androidx.core.app.ComponentActivity  
isNullOrBlank #androidx.core.app.ComponentActivity  
isUserSeeking #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  	presenter #androidx.core.app.ComponentActivity  songAdapter #androidx.core.app.ComponentActivity  OnQueryTextListener .androidx.core.app.ComponentActivity.SearchView  OnSeekBarChangeListener +androidx.core.app.ComponentActivity.SeekBar  Action $androidx.core.app.NotificationCompat  Builder $androidx.core.app.NotificationCompat  VISIBILITY_PUBLIC $androidx.core.app.NotificationCompat  	addAction ,androidx.core.app.NotificationCompat.Builder  build ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setLargeIcon ,androidx.core.app.NotificationCompat.Builder  setOnlyAlertOnce ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  setStyle ,androidx.core.app.NotificationCompat.Builder  
setVisibility ,androidx.core.app.NotificationCompat.Builder  	ArrayList &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  EXTRA_PLAYLIST &androidx.fragment.app.FragmentActivity  
EXTRA_SONG &androidx.fragment.app.FragmentActivity  Glide &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  LinearLayoutManager &androidx.fragment.app.FragmentActivity  List &androidx.fragment.app.FragmentActivity  
MainPresenter &androidx.fragment.app.FragmentActivity  MusicRepository &androidx.fragment.app.FragmentActivity  PlayerActivity &androidx.fragment.app.FragmentActivity  PlayerContract &androidx.fragment.app.FragmentActivity  PlayerPresenter &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  RequestOptions &androidx.fragment.app.FragmentActivity  RoundedCorners &androidx.fragment.app.FragmentActivity  
SearchView &androidx.fragment.app.FragmentActivity  SeekBar &androidx.fragment.app.FragmentActivity  Song &androidx.fragment.app.FragmentActivity  SongAdapter &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  currentTime &androidx.fragment.app.FragmentActivity  	emptyList &androidx.fragment.app.FragmentActivity  format &androidx.fragment.app.FragmentActivity  
formatTime &androidx.fragment.app.FragmentActivity  
isNullOrBlank &androidx.fragment.app.FragmentActivity  
isUserSeeking &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  onResume &androidx.fragment.app.FragmentActivity  	presenter &androidx.fragment.app.FragmentActivity  songAdapter &androidx.fragment.app.FragmentActivity  OnQueryTextListener 1androidx.fragment.app.FragmentActivity.SearchView  OnSeekBarChangeListener .androidx.fragment.app.FragmentActivity.SeekBar  
MediaStyle %androidx.media.app.NotificationCompat  setShowActionsInCompactView 0androidx.media.app.NotificationCompat.MediaStyle  AudioAttributes androidx.media3.common  C androidx.media3.common  	MediaItem androidx.media3.common  
MediaMetadata androidx.media3.common  Player androidx.media3.common  Builder &androidx.media3.common.AudioAttributes  build .androidx.media3.common.AudioAttributes.Builder  setContentType .androidx.media3.common.AudioAttributes.Builder  setUsage .androidx.media3.common.AudioAttributes.Builder  AUDIO_CONTENT_TYPE_MUSIC androidx.media3.common.C  USAGE_MEDIA androidx.media3.common.C  Builder  androidx.media3.common.MediaItem  
mediaMetadata  androidx.media3.common.MediaItem  build (androidx.media3.common.MediaItem.Builder  
setMediaId (androidx.media3.common.MediaItem.Builder  setMediaMetadata (androidx.media3.common.MediaItem.Builder  setUri (androidx.media3.common.MediaItem.Builder  Builder $androidx.media3.common.MediaMetadata  artist $androidx.media3.common.MediaMetadata  
artworkUri $androidx.media3.common.MediaMetadata  title $androidx.media3.common.MediaMetadata  build ,androidx.media3.common.MediaMetadata.Builder  
setAlbumTitle ,androidx.media3.common.MediaMetadata.Builder  	setArtist ,androidx.media3.common.MediaMetadata.Builder  
setArtworkUri ,androidx.media3.common.MediaMetadata.Builder  setTitle ,androidx.media3.common.MediaMetadata.Builder  Listener androidx.media3.common.Player  STATE_BUFFERING androidx.media3.common.Player  STATE_ENDED androidx.media3.common.Player  
STATE_IDLE androidx.media3.common.Player  STATE_READY androidx.media3.common.Player  addListener androidx.media3.common.Player  currentMediaItem androidx.media3.common.Player  	isPlaying androidx.media3.common.Player  pause androidx.media3.common.Player  play androidx.media3.common.Player  release androidx.media3.common.Player  
seekToNext androidx.media3.common.Player  seekToPrevious androidx.media3.common.Player  	ExoPlayer androidx.media3.exoplayer  Builder #androidx.media3.exoplayer.ExoPlayer  addListener #androidx.media3.exoplayer.ExoPlayer  pause #androidx.media3.exoplayer.ExoPlayer  play #androidx.media3.exoplayer.ExoPlayer  
seekToNext #androidx.media3.exoplayer.ExoPlayer  seekToPrevious #androidx.media3.exoplayer.ExoPlayer  build +androidx.media3.exoplayer.ExoPlayer.Builder  setAudioAttributes +androidx.media3.exoplayer.ExoPlayer.Builder  setHandleAudioBecomingNoisy +androidx.media3.exoplayer.ExoPlayer.Builder  MediaController androidx.media3.session  MediaSession androidx.media3.session  MediaSessionService androidx.media3.session  SessionToken androidx.media3.session  Builder 'androidx.media3.session.MediaController  addListener 'androidx.media3.session.MediaController  currentPosition 'androidx.media3.session.MediaController  duration 'androidx.media3.session.MediaController  	isPlaying 'androidx.media3.session.MediaController  let 'androidx.media3.session.MediaController  pause 'androidx.media3.session.MediaController  play 'androidx.media3.session.MediaController  prepare 'androidx.media3.session.MediaController  release 'androidx.media3.session.MediaController  seekTo 'androidx.media3.session.MediaController  
seekToNext 'androidx.media3.session.MediaController  seekToPrevious 'androidx.media3.session.MediaController  
setMediaItems 'androidx.media3.session.MediaController  
buildAsync /androidx.media3.session.MediaController.Builder  Builder $androidx.media3.session.MediaSession  ControllerInfo $androidx.media3.session.MediaSession  let $androidx.media3.session.MediaSession  mediaSession $androidx.media3.session.MediaSession  player $androidx.media3.session.MediaSession  release $androidx.media3.session.MediaSession  run $androidx.media3.session.MediaSession  token $androidx.media3.session.MediaSession  build ,androidx.media3.session.MediaSession.Builder  setSessionActivity ,androidx.media3.session.MediaSession.Builder  packageName 3androidx.media3.session.MediaSession.ControllerInfo  ACTION_NEXT +androidx.media3.session.MediaSessionService  ACTION_PAUSE +androidx.media3.session.MediaSessionService  ACTION_PLAY +androidx.media3.session.MediaSessionService  ACTION_PREVIOUS +androidx.media3.session.MediaSessionService  AudioAttributes +androidx.media3.session.MediaSessionService  Boolean +androidx.media3.session.MediaSessionService  C +androidx.media3.session.MediaSessionService  	ExoPlayer +androidx.media3.session.MediaSessionService  Int +androidx.media3.session.MediaSessionService  Intent +androidx.media3.session.MediaSessionService  Log +androidx.media3.session.MediaSessionService  MainActivity +androidx.media3.session.MediaSessionService  	MediaItem +androidx.media3.session.MediaSessionService  
MediaMetadata +androidx.media3.session.MediaSessionService  MediaSession +androidx.media3.session.MediaSessionService  NotificationHelper +androidx.media3.session.MediaSessionService  
PendingIntent +androidx.media3.session.MediaSessionService  Player +androidx.media3.session.MediaSessionService  START_STICKY +androidx.media3.session.MediaSessionService  TAG +androidx.media3.session.MediaSessionService  android +androidx.media3.session.MediaSessionService  java +androidx.media3.session.MediaSessionService  let +androidx.media3.session.MediaSessionService  mediaSession +androidx.media3.session.MediaSessionService  onCreate +androidx.media3.session.MediaSessionService  	onDestroy +androidx.media3.session.MediaSessionService  run +androidx.media3.session.MediaSessionService  updateNotification +androidx.media3.session.MediaSessionService  Listener 2androidx.media3.session.MediaSessionService.Player  DiffUtil androidx.recyclerview.widget  LinearLayoutManager androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  Boolean (androidx.recyclerview.widget.ListAdapter  Glide (androidx.recyclerview.widget.ListAdapter  LayoutInflater (androidx.recyclerview.widget.ListAdapter  R (androidx.recyclerview.widget.ListAdapter  RequestOptions (androidx.recyclerview.widget.ListAdapter  RoundedCorners (androidx.recyclerview.widget.ListAdapter  String (androidx.recyclerview.widget.ListAdapter  View (androidx.recyclerview.widget.ListAdapter  android (androidx.recyclerview.widget.ListAdapter  currentList (androidx.recyclerview.widget.ListAdapter  indexOfFirst (androidx.recyclerview.widget.ListAdapter  mutableMapOf (androidx.recyclerview.widget.ListAdapter  onSongClick (androidx.recyclerview.widget.ListAdapter  set (androidx.recyclerview.widget.ListAdapter  
LayoutManager )androidx.recyclerview.widget.RecyclerView  LinearLayoutManager )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  apply )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  setHasFixedSize )androidx.recyclerview.widget.RecyclerView  songAdapter )androidx.recyclerview.widget.RecyclerView  
visibility )androidx.recyclerview.widget.RecyclerView  Boolean 1androidx.recyclerview.widget.RecyclerView.Adapter  Glide 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  RequestOptions 1androidx.recyclerview.widget.RecyclerView.Adapter  RoundedCorners 1androidx.recyclerview.widget.RecyclerView.Adapter  String 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  android 1androidx.recyclerview.widget.RecyclerView.Adapter  currentList 1androidx.recyclerview.widget.RecyclerView.Adapter  indexOfFirst 1androidx.recyclerview.widget.RecyclerView.Adapter  mutableMapOf 1androidx.recyclerview.widget.RecyclerView.Adapter  onSongClick 1androidx.recyclerview.widget.RecyclerView.Adapter  set 1androidx.recyclerview.widget.RecyclerView.Adapter  Glide 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  RequestOptions 4androidx.recyclerview.widget.RecyclerView.ViewHolder  RoundedCorners 4androidx.recyclerview.widget.RecyclerView.ViewHolder  View 4androidx.recyclerview.widget.RecyclerView.ViewHolder  android 4androidx.recyclerview.widget.RecyclerView.ViewHolder  currentList 4androidx.recyclerview.widget.RecyclerView.ViewHolder  itemView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onSongClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  SwipeRefreshLayout "androidx.swiperefreshlayout.widget  OnRefreshListener 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  isRefreshing 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  setColorSchemeResources 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  setOnRefreshListener 5androidx.swiperefreshlayout.widget.SwipeRefreshLayout  <SAM-CONSTRUCTOR> Gandroidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener  Glide com.bumptech.glide  RequestBuilder com.bumptech.glide  RequestManager com.bumptech.glide  with com.bumptech.glide.Glide  apply !com.bumptech.glide.RequestBuilder  into !com.bumptech.glide.RequestBuilder  load !com.bumptech.glide.RequestBuilder  asBitmap !com.bumptech.glide.RequestManager  load !com.bumptech.glide.RequestManager  RoundedCorners 'com.bumptech.glide.load.resource.bitmap  RequestOptions com.bumptech.glide.request  error )com.bumptech.glide.request.RequestOptions  placeholder )com.bumptech.glide.request.RequestOptions  	transform )com.bumptech.glide.request.RequestOptions  CustomTarget !com.bumptech.glide.request.target  
ViewTarget !com.bumptech.glide.request.target  createNotification .com.bumptech.glide.request.target.CustomTarget  
Transition %com.bumptech.glide.request.transition  ListenableFuture !com.google.common.util.concurrent  addListener 2com.google.common.util.concurrent.ListenableFuture  cancel 2com.google.common.util.concurrent.ListenableFuture  get 2com.google.common.util.concurrent.ListenableFuture  Application com.nauh.musicplayer  Log com.nauh.musicplayer  MusicPlayerApplication com.nauh.musicplayer  R com.nauh.musicplayer  TAG com.nauh.musicplayer  Log +com.nauh.musicplayer.MusicPlayerApplication  TAG +com.nauh.musicplayer.MusicPlayerApplication  Log 5com.nauh.musicplayer.MusicPlayerApplication.Companion  TAG 5com.nauh.musicplayer.MusicPlayerApplication.Companion  colorAccent com.nauh.musicplayer.R.color  colorPrimary com.nauh.musicplayer.R.color  
ic_music_note com.nauh.musicplayer.R.drawable  ic_pause com.nauh.musicplayer.R.drawable  ic_play com.nauh.musicplayer.R.drawable  	ic_repeat com.nauh.musicplayer.R.drawable  
ic_repeat_one com.nauh.musicplayer.R.drawable  ic_skip_next com.nauh.musicplayer.R.drawable  ic_skip_previous com.nauh.musicplayer.R.drawable  placeholder_album_art com.nauh.musicplayer.R.drawable  btnBack com.nauh.musicplayer.R.id  btnNext com.nauh.musicplayer.R.id  btnPlayPause com.nauh.musicplayer.R.id  btnPrevious com.nauh.musicplayer.R.id  	btnRepeat com.nauh.musicplayer.R.id  
btnShuffle com.nauh.musicplayer.R.id  
ivAlbumArt com.nauh.musicplayer.R.id  ivPlayingIndicator com.nauh.musicplayer.R.id  progressBar com.nauh.musicplayer.R.id  recyclerViewSongs com.nauh.musicplayer.R.id  
searchView com.nauh.musicplayer.R.id  seekBar com.nauh.musicplayer.R.id  swipeRefreshLayout com.nauh.musicplayer.R.id  tvArtist com.nauh.musicplayer.R.id  tvArtistName com.nauh.musicplayer.R.id  
tvCurrentTime com.nauh.musicplayer.R.id  
tvDuration com.nauh.musicplayer.R.id  tvEmptyState com.nauh.musicplayer.R.id  tvSongTitle com.nauh.musicplayer.R.id  tvTitle com.nauh.musicplayer.R.id  tvTotalTime com.nauh.musicplayer.R.id  
activity_main com.nauh.musicplayer.R.layout  activity_player com.nauh.musicplayer.R.layout  	item_song com.nauh.musicplayer.R.layout  Boolean com.nauh.musicplayer.contract  Int com.nauh.musicplayer.contract  List com.nauh.musicplayer.contract  Long com.nauh.musicplayer.contract  MainContract com.nauh.musicplayer.contract  PlayerContract com.nauh.musicplayer.contract  Song com.nauh.musicplayer.contract  String com.nauh.musicplayer.contract  View com.nauh.musicplayer.contract  Boolean *com.nauh.musicplayer.contract.MainContract  List *com.nauh.musicplayer.contract.MainContract  	Presenter *com.nauh.musicplayer.contract.MainContract  Song *com.nauh.musicplayer.contract.MainContract  String *com.nauh.musicplayer.contract.MainContract  View *com.nauh.musicplayer.contract.MainContract  hideEmptyState /com.nauh.musicplayer.contract.MainContract.View  hideLoading /com.nauh.musicplayer.contract.MainContract.View  navigateToPlayer /com.nauh.musicplayer.contract.MainContract.View  showEmptyState /com.nauh.musicplayer.contract.MainContract.View  	showError /com.nauh.musicplayer.contract.MainContract.View  showLoading /com.nauh.musicplayer.contract.MainContract.View  	showSongs /com.nauh.musicplayer.contract.MainContract.View  updateSongPlayingState /com.nauh.musicplayer.contract.MainContract.View  Boolean ,com.nauh.musicplayer.contract.PlayerContract  Int ,com.nauh.musicplayer.contract.PlayerContract  List ,com.nauh.musicplayer.contract.PlayerContract  Long ,com.nauh.musicplayer.contract.PlayerContract  	Presenter ,com.nauh.musicplayer.contract.PlayerContract  
RepeatMode ,com.nauh.musicplayer.contract.PlayerContract  Song ,com.nauh.musicplayer.contract.PlayerContract  String ,com.nauh.musicplayer.contract.PlayerContract  View ,com.nauh.musicplayer.contract.PlayerContract  ALL 7com.nauh.musicplayer.contract.PlayerContract.RepeatMode  OFF 7com.nauh.musicplayer.contract.PlayerContract.RepeatMode  ONE 7com.nauh.musicplayer.contract.PlayerContract.RepeatMode  enableNextButton 1com.nauh.musicplayer.contract.PlayerContract.View  enablePreviousButton 1com.nauh.musicplayer.contract.PlayerContract.View  hideLoading 1com.nauh.musicplayer.contract.PlayerContract.View  
showBuffering 1com.nauh.musicplayer.contract.PlayerContract.View  	showError 1com.nauh.musicplayer.contract.PlayerContract.View  showLoading 1com.nauh.musicplayer.contract.PlayerContract.View  showRepeatState 1com.nauh.musicplayer.contract.PlayerContract.View  showShuffleState 1com.nauh.musicplayer.contract.PlayerContract.View  showSongInfo 1com.nauh.musicplayer.contract.PlayerContract.View  updatePlayPauseButton 1com.nauh.musicplayer.contract.PlayerContract.View  updatePlaylist 1com.nauh.musicplayer.contract.PlayerContract.View  updateProgress 1com.nauh.musicplayer.contract.PlayerContract.View  
updateSeekBar 1com.nauh.musicplayer.contract.PlayerContract.View  Int com.nauh.musicplayer.data.model  List com.nauh.musicplayer.data.model  Long com.nauh.musicplayer.data.model  
Parcelable com.nauh.musicplayer.data.model  	Parcelize com.nauh.musicplayer.data.model  Playlist com.nauh.musicplayer.data.model  Song com.nauh.musicplayer.data.model  String com.nauh.musicplayer.data.model  System com.nauh.musicplayer.data.model  	emptyList com.nauh.musicplayer.data.model  format com.nauh.musicplayer.data.model  sumOf com.nauh.musicplayer.data.model  String (com.nauh.musicplayer.data.model.Playlist  format (com.nauh.musicplayer.data.model.Playlist  getTotalDuration (com.nauh.musicplayer.data.model.Playlist  songs (com.nauh.musicplayer.data.model.Playlist  sumOf (com.nauh.musicplayer.data.model.Playlist  String $com.nauh.musicplayer.data.model.Song  album $com.nauh.musicplayer.data.model.Song  artist $com.nauh.musicplayer.data.model.Song  
artworkUrl $com.nauh.musicplayer.data.model.Song  duration $com.nauh.musicplayer.data.model.Song  format $com.nauh.musicplayer.data.model.Song  getFormattedDuration $com.nauh.musicplayer.data.model.Song  
getMediaId $com.nauh.musicplayer.data.model.Song  id $com.nauh.musicplayer.data.model.Song  let $com.nauh.musicplayer.data.model.Song  	streamUrl $com.nauh.musicplayer.data.model.Song  title $com.nauh.musicplayer.data.model.Song  Dispatchers $com.nauh.musicplayer.data.repository  List $com.nauh.musicplayer.data.repository  MusicRepository $com.nauh.musicplayer.data.repository  Song $com.nauh.musicplayer.data.repository  String $com.nauh.musicplayer.data.repository  contains $com.nauh.musicplayer.data.repository  filter $com.nauh.musicplayer.data.repository  find $com.nauh.musicplayer.data.repository  getSongs $com.nauh.musicplayer.data.repository  isBlank $com.nauh.musicplayer.data.repository  listOf $com.nauh.musicplayer.data.repository  withContext $com.nauh.musicplayer.data.repository  Dispatchers 4com.nauh.musicplayer.data.repository.MusicRepository  Song 4com.nauh.musicplayer.data.repository.MusicRepository  contains 4com.nauh.musicplayer.data.repository.MusicRepository  filter 4com.nauh.musicplayer.data.repository.MusicRepository  find 4com.nauh.musicplayer.data.repository.MusicRepository  getSongs 4com.nauh.musicplayer.data.repository.MusicRepository  isBlank 4com.nauh.musicplayer.data.repository.MusicRepository  listOf 4com.nauh.musicplayer.data.repository.MusicRepository  searchSongs 4com.nauh.musicplayer.data.repository.MusicRepository  withContext 4com.nauh.musicplayer.data.repository.MusicRepository  Boolean com.nauh.musicplayer.presenter  Context com.nauh.musicplayer.presenter  CoroutineScope com.nauh.musicplayer.presenter  Dispatchers com.nauh.musicplayer.presenter  	Exception com.nauh.musicplayer.presenter  Int com.nauh.musicplayer.presenter  Job com.nauh.musicplayer.presenter  List com.nauh.musicplayer.presenter  Log com.nauh.musicplayer.presenter  Long com.nauh.musicplayer.presenter  MainContract com.nauh.musicplayer.presenter  
MainPresenter com.nauh.musicplayer.presenter  MusicRepository com.nauh.musicplayer.presenter  MusicServiceConnection com.nauh.musicplayer.presenter  PlayerContract com.nauh.musicplayer.presenter  PlayerPresenter com.nauh.musicplayer.presenter  Song com.nauh.musicplayer.presenter  String com.nauh.musicplayer.presenter  TAG com.nauh.musicplayer.presenter  apply com.nauh.musicplayer.presenter  currentSongs com.nauh.musicplayer.presenter  	emptyList com.nauh.musicplayer.presenter  forEach com.nauh.musicplayer.presenter  indexOfFirst com.nauh.musicplayer.presenter  
isNotEmpty com.nauh.musicplayer.presenter  launch com.nauh.musicplayer.presenter  let com.nauh.musicplayer.presenter  musicRepository com.nauh.musicplayer.presenter  takeIf com.nauh.musicplayer.presenter  view com.nauh.musicplayer.presenter  withContext com.nauh.musicplayer.presenter  	Presenter +com.nauh.musicplayer.presenter.MainContract  View +com.nauh.musicplayer.presenter.MainContract  Boolean ,com.nauh.musicplayer.presenter.MainPresenter  CoroutineScope ,com.nauh.musicplayer.presenter.MainPresenter  Dispatchers ,com.nauh.musicplayer.presenter.MainPresenter  	Exception ,com.nauh.musicplayer.presenter.MainPresenter  Job ,com.nauh.musicplayer.presenter.MainPresenter  List ,com.nauh.musicplayer.presenter.MainPresenter  Log ,com.nauh.musicplayer.presenter.MainPresenter  MainContract ,com.nauh.musicplayer.presenter.MainPresenter  MusicRepository ,com.nauh.musicplayer.presenter.MainPresenter  Song ,com.nauh.musicplayer.presenter.MainPresenter  String ,com.nauh.musicplayer.presenter.MainPresenter  TAG ,com.nauh.musicplayer.presenter.MainPresenter  
attachView ,com.nauh.musicplayer.presenter.MainPresenter  currentPlayingSongId ,com.nauh.musicplayer.presenter.MainPresenter  currentSongs ,com.nauh.musicplayer.presenter.MainPresenter  
detachView ,com.nauh.musicplayer.presenter.MainPresenter  	emptyList ,com.nauh.musicplayer.presenter.MainPresenter  
isNotEmpty ,com.nauh.musicplayer.presenter.MainPresenter  launch ,com.nauh.musicplayer.presenter.MainPresenter  	loadSongs ,com.nauh.musicplayer.presenter.MainPresenter  musicRepository ,com.nauh.musicplayer.presenter.MainPresenter  	onRefresh ,com.nauh.musicplayer.presenter.MainPresenter  
onSongClicked ,com.nauh.musicplayer.presenter.MainPresenter  presenterScope ,com.nauh.musicplayer.presenter.MainPresenter  searchSongs ,com.nauh.musicplayer.presenter.MainPresenter  view ,com.nauh.musicplayer.presenter.MainPresenter  withContext ,com.nauh.musicplayer.presenter.MainPresenter  CoroutineScope 6com.nauh.musicplayer.presenter.MainPresenter.Companion  Dispatchers 6com.nauh.musicplayer.presenter.MainPresenter.Companion  Job 6com.nauh.musicplayer.presenter.MainPresenter.Companion  Log 6com.nauh.musicplayer.presenter.MainPresenter.Companion  TAG 6com.nauh.musicplayer.presenter.MainPresenter.Companion  currentSongs 6com.nauh.musicplayer.presenter.MainPresenter.Companion  	emptyList 6com.nauh.musicplayer.presenter.MainPresenter.Companion  
isNotEmpty 6com.nauh.musicplayer.presenter.MainPresenter.Companion  launch 6com.nauh.musicplayer.presenter.MainPresenter.Companion  musicRepository 6com.nauh.musicplayer.presenter.MainPresenter.Companion  view 6com.nauh.musicplayer.presenter.MainPresenter.Companion  withContext 6com.nauh.musicplayer.presenter.MainPresenter.Companion  View 9com.nauh.musicplayer.presenter.MainPresenter.MainContract  PlaybackStateListener 5com.nauh.musicplayer.presenter.MusicServiceConnection  	Presenter -com.nauh.musicplayer.presenter.PlayerContract  View -com.nauh.musicplayer.presenter.PlayerContract  Boolean .com.nauh.musicplayer.presenter.PlayerPresenter  Context .com.nauh.musicplayer.presenter.PlayerPresenter  Int .com.nauh.musicplayer.presenter.PlayerPresenter  List .com.nauh.musicplayer.presenter.PlayerPresenter  Log .com.nauh.musicplayer.presenter.PlayerPresenter  Long .com.nauh.musicplayer.presenter.PlayerPresenter  MusicServiceConnection .com.nauh.musicplayer.presenter.PlayerPresenter  PlayerContract .com.nauh.musicplayer.presenter.PlayerPresenter  Song .com.nauh.musicplayer.presenter.PlayerPresenter  String .com.nauh.musicplayer.presenter.PlayerPresenter  TAG .com.nauh.musicplayer.presenter.PlayerPresenter  apply .com.nauh.musicplayer.presenter.PlayerPresenter  
attachView .com.nauh.musicplayer.presenter.PlayerPresenter  context .com.nauh.musicplayer.presenter.PlayerPresenter  currentIndex .com.nauh.musicplayer.presenter.PlayerPresenter  currentPlaylist .com.nauh.musicplayer.presenter.PlayerPresenter  currentSong .com.nauh.musicplayer.presenter.PlayerPresenter  
detachView .com.nauh.musicplayer.presenter.PlayerPresenter  	emptyList .com.nauh.musicplayer.presenter.PlayerPresenter  getDuration .com.nauh.musicplayer.presenter.PlayerPresenter  indexOfFirst .com.nauh.musicplayer.presenter.PlayerPresenter  initializePlayer .com.nauh.musicplayer.presenter.PlayerPresenter  
isShuffled .com.nauh.musicplayer.presenter.PlayerPresenter  let .com.nauh.musicplayer.presenter.PlayerPresenter  musicServiceConnection .com.nauh.musicplayer.presenter.PlayerPresenter  
onSongChanged .com.nauh.musicplayer.presenter.PlayerPresenter  	playPause .com.nauh.musicplayer.presenter.PlayerPresenter  
repeatMode .com.nauh.musicplayer.presenter.PlayerPresenter  seekTo .com.nauh.musicplayer.presenter.PlayerPresenter  
skipToNext .com.nauh.musicplayer.presenter.PlayerPresenter  skipToPrevious .com.nauh.musicplayer.presenter.PlayerPresenter  takeIf .com.nauh.musicplayer.presenter.PlayerPresenter  toggleRepeat .com.nauh.musicplayer.presenter.PlayerPresenter  
toggleShuffle .com.nauh.musicplayer.presenter.PlayerPresenter  updateNavigationButtons .com.nauh.musicplayer.presenter.PlayerPresenter  view .com.nauh.musicplayer.presenter.PlayerPresenter  Log 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  MusicServiceConnection 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  PlayerContract 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  TAG 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  apply 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  	emptyList 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  indexOfFirst 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  let 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  takeIf 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  View =com.nauh.musicplayer.presenter.PlayerPresenter.PlayerContract  ACTION_NEXT com.nauh.musicplayer.service  ACTION_PAUSE com.nauh.musicplayer.service  ACTION_PLAY com.nauh.musicplayer.service  ACTION_PREVIOUS com.nauh.musicplayer.service  AudioAttributes com.nauh.musicplayer.service  Bitmap com.nauh.musicplayer.service  Boolean com.nauh.musicplayer.service  Build com.nauh.musicplayer.service  C com.nauh.musicplayer.service  CHANNEL_DESCRIPTION com.nauh.musicplayer.service  
CHANNEL_ID com.nauh.musicplayer.service  CHANNEL_NAME com.nauh.musicplayer.service  
ComponentName com.nauh.musicplayer.service  Context com.nauh.musicplayer.service  CustomTarget com.nauh.musicplayer.service  Drawable com.nauh.musicplayer.service  	Exception com.nauh.musicplayer.service  	ExoPlayer com.nauh.musicplayer.service  Glide com.nauh.musicplayer.service  Handler com.nauh.musicplayer.service  Int com.nauh.musicplayer.service  Intent com.nauh.musicplayer.service  List com.nauh.musicplayer.service  ListenableFuture com.nauh.musicplayer.service  Log com.nauh.musicplayer.service  Long com.nauh.musicplayer.service  Looper com.nauh.musicplayer.service  MainActivity com.nauh.musicplayer.service  MediaController com.nauh.musicplayer.service  	MediaItem com.nauh.musicplayer.service  
MediaMetadata com.nauh.musicplayer.service  MediaSession com.nauh.musicplayer.service  MediaSessionService com.nauh.musicplayer.service  MusicService com.nauh.musicplayer.service  MusicServiceConnection com.nauh.musicplayer.service  NOTIFICATION_ID com.nauh.musicplayer.service  Notification com.nauh.musicplayer.service  NotificationChannel com.nauh.musicplayer.service  NotificationCompat com.nauh.musicplayer.service  NotificationHelper com.nauh.musicplayer.service  NotificationManager com.nauh.musicplayer.service  PROGRESS_UPDATE_INTERVAL com.nauh.musicplayer.service  Pair com.nauh.musicplayer.service  
PendingIntent com.nauh.musicplayer.service  PlaybackStateListener com.nauh.musicplayer.service  Player com.nauh.musicplayer.service  PlayerActivity com.nauh.musicplayer.service  R com.nauh.musicplayer.service  Runnable com.nauh.musicplayer.service  START_STICKY com.nauh.musicplayer.service  SessionToken com.nauh.musicplayer.service  Song com.nauh.musicplayer.service  String com.nauh.musicplayer.service  TAG com.nauh.musicplayer.service  
Transition com.nauh.musicplayer.service  android com.nauh.musicplayer.service  androidx com.nauh.musicplayer.service  apply com.nauh.musicplayer.service  createMediaItem com.nauh.musicplayer.service  createNotification com.nauh.musicplayer.service  java com.nauh.musicplayer.service  let com.nauh.musicplayer.service  listOf com.nauh.musicplayer.service  map com.nauh.musicplayer.service  mediaController com.nauh.musicplayer.service  mediaSession com.nauh.musicplayer.service  playbackStateListener com.nauh.musicplayer.service  progressUpdateHandler com.nauh.musicplayer.service  run com.nauh.musicplayer.service  startProgressUpdates com.nauh.musicplayer.service  stopProgressUpdates com.nauh.musicplayer.service  updateNotification com.nauh.musicplayer.service  ControllerInfo )com.nauh.musicplayer.service.MediaSession  ACTION_NEXT )com.nauh.musicplayer.service.MusicService  ACTION_PAUSE )com.nauh.musicplayer.service.MusicService  ACTION_PLAY )com.nauh.musicplayer.service.MusicService  ACTION_PREVIOUS )com.nauh.musicplayer.service.MusicService  AudioAttributes )com.nauh.musicplayer.service.MusicService  Boolean )com.nauh.musicplayer.service.MusicService  C )com.nauh.musicplayer.service.MusicService  	Companion )com.nauh.musicplayer.service.MusicService  	ExoPlayer )com.nauh.musicplayer.service.MusicService  Int )com.nauh.musicplayer.service.MusicService  Intent )com.nauh.musicplayer.service.MusicService  Log )com.nauh.musicplayer.service.MusicService  MainActivity )com.nauh.musicplayer.service.MusicService  	MediaItem )com.nauh.musicplayer.service.MusicService  
MediaMetadata )com.nauh.musicplayer.service.MusicService  MediaSession )com.nauh.musicplayer.service.MusicService  NotificationHelper )com.nauh.musicplayer.service.MusicService  
PendingIntent )com.nauh.musicplayer.service.MusicService  Player )com.nauh.musicplayer.service.MusicService  START_STICKY )com.nauh.musicplayer.service.MusicService  Song )com.nauh.musicplayer.service.MusicService  TAG )com.nauh.musicplayer.service.MusicService  android )com.nauh.musicplayer.service.MusicService  createMediaItem )com.nauh.musicplayer.service.MusicService  initializeMediaSession )com.nauh.musicplayer.service.MusicService  initializeNotificationHelper )com.nauh.musicplayer.service.MusicService  initializePlayer )com.nauh.musicplayer.service.MusicService  java )com.nauh.musicplayer.service.MusicService  let )com.nauh.musicplayer.service.MusicService  mediaSession )com.nauh.musicplayer.service.MusicService  notificationHelper )com.nauh.musicplayer.service.MusicService  player )com.nauh.musicplayer.service.MusicService  run )com.nauh.musicplayer.service.MusicService  updateNotification )com.nauh.musicplayer.service.MusicService  ACTION_NEXT 3com.nauh.musicplayer.service.MusicService.Companion  ACTION_PAUSE 3com.nauh.musicplayer.service.MusicService.Companion  ACTION_PLAY 3com.nauh.musicplayer.service.MusicService.Companion  ACTION_PREVIOUS 3com.nauh.musicplayer.service.MusicService.Companion  AudioAttributes 3com.nauh.musicplayer.service.MusicService.Companion  C 3com.nauh.musicplayer.service.MusicService.Companion  	ExoPlayer 3com.nauh.musicplayer.service.MusicService.Companion  Intent 3com.nauh.musicplayer.service.MusicService.Companion  Log 3com.nauh.musicplayer.service.MusicService.Companion  MainActivity 3com.nauh.musicplayer.service.MusicService.Companion  	MediaItem 3com.nauh.musicplayer.service.MusicService.Companion  
MediaMetadata 3com.nauh.musicplayer.service.MusicService.Companion  MediaSession 3com.nauh.musicplayer.service.MusicService.Companion  NotificationHelper 3com.nauh.musicplayer.service.MusicService.Companion  
PendingIntent 3com.nauh.musicplayer.service.MusicService.Companion  START_STICKY 3com.nauh.musicplayer.service.MusicService.Companion  TAG 3com.nauh.musicplayer.service.MusicService.Companion  android 3com.nauh.musicplayer.service.MusicService.Companion  createMediaItem 3com.nauh.musicplayer.service.MusicService.Companion  java 3com.nauh.musicplayer.service.MusicService.Companion  let 3com.nauh.musicplayer.service.MusicService.Companion  mediaSession 3com.nauh.musicplayer.service.MusicService.Companion  run 3com.nauh.musicplayer.service.MusicService.Companion  updateNotification 3com.nauh.musicplayer.service.MusicService.Companion  ControllerInfo 6com.nauh.musicplayer.service.MusicService.MediaSession  Listener 0com.nauh.musicplayer.service.MusicService.Player  Boolean 3com.nauh.musicplayer.service.MusicServiceConnection  
ComponentName 3com.nauh.musicplayer.service.MusicServiceConnection  Context 3com.nauh.musicplayer.service.MusicServiceConnection  	Exception 3com.nauh.musicplayer.service.MusicServiceConnection  Handler 3com.nauh.musicplayer.service.MusicServiceConnection  Int 3com.nauh.musicplayer.service.MusicServiceConnection  Intent 3com.nauh.musicplayer.service.MusicServiceConnection  List 3com.nauh.musicplayer.service.MusicServiceConnection  ListenableFuture 3com.nauh.musicplayer.service.MusicServiceConnection  Log 3com.nauh.musicplayer.service.MusicServiceConnection  Long 3com.nauh.musicplayer.service.MusicServiceConnection  Looper 3com.nauh.musicplayer.service.MusicServiceConnection  MediaController 3com.nauh.musicplayer.service.MusicServiceConnection  	MediaItem 3com.nauh.musicplayer.service.MusicServiceConnection  MusicService 3com.nauh.musicplayer.service.MusicServiceConnection  PROGRESS_UPDATE_INTERVAL 3com.nauh.musicplayer.service.MusicServiceConnection  Pair 3com.nauh.musicplayer.service.MusicServiceConnection  PlaybackStateListener 3com.nauh.musicplayer.service.MusicServiceConnection  Player 3com.nauh.musicplayer.service.MusicServiceConnection  Runnable 3com.nauh.musicplayer.service.MusicServiceConnection  SessionToken 3com.nauh.musicplayer.service.MusicServiceConnection  Song 3com.nauh.musicplayer.service.MusicServiceConnection  String 3com.nauh.musicplayer.service.MusicServiceConnection  TAG 3com.nauh.musicplayer.service.MusicServiceConnection  apply 3com.nauh.musicplayer.service.MusicServiceConnection  connect 3com.nauh.musicplayer.service.MusicServiceConnection  context 3com.nauh.musicplayer.service.MusicServiceConnection  createMediaItem 3com.nauh.musicplayer.service.MusicServiceConnection  
disconnect 3com.nauh.musicplayer.service.MusicServiceConnection  getDuration 3com.nauh.musicplayer.service.MusicServiceConnection  isConnected 3com.nauh.musicplayer.service.MusicServiceConnection  java 3com.nauh.musicplayer.service.MusicServiceConnection  let 3com.nauh.musicplayer.service.MusicServiceConnection  listOf 3com.nauh.musicplayer.service.MusicServiceConnection  map 3com.nauh.musicplayer.service.MusicServiceConnection  mediaController 3com.nauh.musicplayer.service.MusicServiceConnection  mediaControllerFuture 3com.nauh.musicplayer.service.MusicServiceConnection  onMediaControllerConnected 3com.nauh.musicplayer.service.MusicServiceConnection  pendingPlaylist 3com.nauh.musicplayer.service.MusicServiceConnection  	playPause 3com.nauh.musicplayer.service.MusicServiceConnection  playPlaylist 3com.nauh.musicplayer.service.MusicServiceConnection  playbackStateListener 3com.nauh.musicplayer.service.MusicServiceConnection  progressUpdateHandler 3com.nauh.musicplayer.service.MusicServiceConnection  progressUpdateRunnable 3com.nauh.musicplayer.service.MusicServiceConnection  seekTo 3com.nauh.musicplayer.service.MusicServiceConnection  setPlaybackStateListener 3com.nauh.musicplayer.service.MusicServiceConnection  
skipToNext 3com.nauh.musicplayer.service.MusicServiceConnection  skipToPrevious 3com.nauh.musicplayer.service.MusicServiceConnection  startProgressUpdates 3com.nauh.musicplayer.service.MusicServiceConnection  stopProgressUpdates 3com.nauh.musicplayer.service.MusicServiceConnection  
ComponentName =com.nauh.musicplayer.service.MusicServiceConnection.Companion  Handler =com.nauh.musicplayer.service.MusicServiceConnection.Companion  Intent =com.nauh.musicplayer.service.MusicServiceConnection.Companion  Log =com.nauh.musicplayer.service.MusicServiceConnection.Companion  Looper =com.nauh.musicplayer.service.MusicServiceConnection.Companion  MediaController =com.nauh.musicplayer.service.MusicServiceConnection.Companion  MusicService =com.nauh.musicplayer.service.MusicServiceConnection.Companion  PROGRESS_UPDATE_INTERVAL =com.nauh.musicplayer.service.MusicServiceConnection.Companion  Pair =com.nauh.musicplayer.service.MusicServiceConnection.Companion  Player =com.nauh.musicplayer.service.MusicServiceConnection.Companion  SessionToken =com.nauh.musicplayer.service.MusicServiceConnection.Companion  TAG =com.nauh.musicplayer.service.MusicServiceConnection.Companion  createMediaItem =com.nauh.musicplayer.service.MusicServiceConnection.Companion  java =com.nauh.musicplayer.service.MusicServiceConnection.Companion  let =com.nauh.musicplayer.service.MusicServiceConnection.Companion  listOf =com.nauh.musicplayer.service.MusicServiceConnection.Companion  map =com.nauh.musicplayer.service.MusicServiceConnection.Companion  mediaController =com.nauh.musicplayer.service.MusicServiceConnection.Companion  playbackStateListener =com.nauh.musicplayer.service.MusicServiceConnection.Companion  progressUpdateHandler =com.nauh.musicplayer.service.MusicServiceConnection.Companion  startProgressUpdates =com.nauh.musicplayer.service.MusicServiceConnection.Companion  stopProgressUpdates =com.nauh.musicplayer.service.MusicServiceConnection.Companion  onConnectionError Icom.nauh.musicplayer.service.MusicServiceConnection.PlaybackStateListener  onPlaybackStateChanged Icom.nauh.musicplayer.service.MusicServiceConnection.PlaybackStateListener  onProgressUpdate Icom.nauh.musicplayer.service.MusicServiceConnection.PlaybackStateListener  Listener :com.nauh.musicplayer.service.MusicServiceConnection.Player  Bitmap /com.nauh.musicplayer.service.NotificationHelper  Boolean /com.nauh.musicplayer.service.NotificationHelper  Build /com.nauh.musicplayer.service.NotificationHelper  CHANNEL_DESCRIPTION /com.nauh.musicplayer.service.NotificationHelper  
CHANNEL_ID /com.nauh.musicplayer.service.NotificationHelper  CHANNEL_NAME /com.nauh.musicplayer.service.NotificationHelper  Context /com.nauh.musicplayer.service.NotificationHelper  CustomTarget /com.nauh.musicplayer.service.NotificationHelper  Drawable /com.nauh.musicplayer.service.NotificationHelper  Glide /com.nauh.musicplayer.service.NotificationHelper  Intent /com.nauh.musicplayer.service.NotificationHelper  Log /com.nauh.musicplayer.service.NotificationHelper  MediaSession /com.nauh.musicplayer.service.NotificationHelper  MusicService /com.nauh.musicplayer.service.NotificationHelper  NOTIFICATION_ID /com.nauh.musicplayer.service.NotificationHelper  Notification /com.nauh.musicplayer.service.NotificationHelper  NotificationChannel /com.nauh.musicplayer.service.NotificationHelper  NotificationCompat /com.nauh.musicplayer.service.NotificationHelper  NotificationManager /com.nauh.musicplayer.service.NotificationHelper  
PendingIntent /com.nauh.musicplayer.service.NotificationHelper  PlayerActivity /com.nauh.musicplayer.service.NotificationHelper  R /com.nauh.musicplayer.service.NotificationHelper  String /com.nauh.musicplayer.service.NotificationHelper  TAG /com.nauh.musicplayer.service.NotificationHelper  
Transition /com.nauh.musicplayer.service.NotificationHelper  androidx /com.nauh.musicplayer.service.NotificationHelper  apply /com.nauh.musicplayer.service.NotificationHelper  context /com.nauh.musicplayer.service.NotificationHelper  createNotification /com.nauh.musicplayer.service.NotificationHelper  createNotificationChannel /com.nauh.musicplayer.service.NotificationHelper  createPendingIntent /com.nauh.musicplayer.service.NotificationHelper  hideNotification /com.nauh.musicplayer.service.NotificationHelper  java /com.nauh.musicplayer.service.NotificationHelper   loadArtworkAndCreateNotification /com.nauh.musicplayer.service.NotificationHelper  notificationManager /com.nauh.musicplayer.service.NotificationHelper  updateNotification /com.nauh.musicplayer.service.NotificationHelper  Build 9com.nauh.musicplayer.service.NotificationHelper.Companion  CHANNEL_DESCRIPTION 9com.nauh.musicplayer.service.NotificationHelper.Companion  
CHANNEL_ID 9com.nauh.musicplayer.service.NotificationHelper.Companion  CHANNEL_NAME 9com.nauh.musicplayer.service.NotificationHelper.Companion  Context 9com.nauh.musicplayer.service.NotificationHelper.Companion  Glide 9com.nauh.musicplayer.service.NotificationHelper.Companion  Intent 9com.nauh.musicplayer.service.NotificationHelper.Companion  Log 9com.nauh.musicplayer.service.NotificationHelper.Companion  MusicService 9com.nauh.musicplayer.service.NotificationHelper.Companion  NOTIFICATION_ID 9com.nauh.musicplayer.service.NotificationHelper.Companion  Notification 9com.nauh.musicplayer.service.NotificationHelper.Companion  NotificationChannel 9com.nauh.musicplayer.service.NotificationHelper.Companion  NotificationCompat 9com.nauh.musicplayer.service.NotificationHelper.Companion  NotificationManager 9com.nauh.musicplayer.service.NotificationHelper.Companion  
PendingIntent 9com.nauh.musicplayer.service.NotificationHelper.Companion  PlayerActivity 9com.nauh.musicplayer.service.NotificationHelper.Companion  R 9com.nauh.musicplayer.service.NotificationHelper.Companion  TAG 9com.nauh.musicplayer.service.NotificationHelper.Companion  androidx 9com.nauh.musicplayer.service.NotificationHelper.Companion  apply 9com.nauh.musicplayer.service.NotificationHelper.Companion  createNotification 9com.nauh.musicplayer.service.NotificationHelper.Companion  java 9com.nauh.musicplayer.service.NotificationHelper.Companion  Listener #com.nauh.musicplayer.service.Player  AppCompatActivity com.nauh.musicplayer.ui  	ArrayList com.nauh.musicplayer.ui  Boolean com.nauh.musicplayer.ui  Bundle com.nauh.musicplayer.ui  EXTRA_PLAYLIST com.nauh.musicplayer.ui  
EXTRA_SONG com.nauh.musicplayer.ui  Glide com.nauh.musicplayer.ui  ImageButton com.nauh.musicplayer.ui  	ImageView com.nauh.musicplayer.ui  Int com.nauh.musicplayer.ui  Intent com.nauh.musicplayer.ui  LinearLayoutManager com.nauh.musicplayer.ui  List com.nauh.musicplayer.ui  Long com.nauh.musicplayer.ui  MainActivity com.nauh.musicplayer.ui  MainContract com.nauh.musicplayer.ui  
MainPresenter com.nauh.musicplayer.ui  MusicRepository com.nauh.musicplayer.ui  PlayerActivity com.nauh.musicplayer.ui  PlayerContract com.nauh.musicplayer.ui  PlayerPresenter com.nauh.musicplayer.ui  ProgressBar com.nauh.musicplayer.ui  R com.nauh.musicplayer.ui  RecyclerView com.nauh.musicplayer.ui  RequestOptions com.nauh.musicplayer.ui  RoundedCorners com.nauh.musicplayer.ui  
SearchView com.nauh.musicplayer.ui  SeekBar com.nauh.musicplayer.ui  Song com.nauh.musicplayer.ui  SongAdapter com.nauh.musicplayer.ui  String com.nauh.musicplayer.ui  SwipeRefreshLayout com.nauh.musicplayer.ui  TextView com.nauh.musicplayer.ui  Toast com.nauh.musicplayer.ui  View com.nauh.musicplayer.ui  apply com.nauh.musicplayer.ui  currentTime com.nauh.musicplayer.ui  	emptyList com.nauh.musicplayer.ui  format com.nauh.musicplayer.ui  
formatTime com.nauh.musicplayer.ui  
isNullOrBlank com.nauh.musicplayer.ui  
isUserSeeking com.nauh.musicplayer.ui  java com.nauh.musicplayer.ui  let com.nauh.musicplayer.ui  	presenter com.nauh.musicplayer.ui  songAdapter com.nauh.musicplayer.ui  	ArrayList $com.nauh.musicplayer.ui.MainActivity  Boolean $com.nauh.musicplayer.ui.MainActivity  Bundle $com.nauh.musicplayer.ui.MainActivity  	Companion $com.nauh.musicplayer.ui.MainActivity  Intent $com.nauh.musicplayer.ui.MainActivity  LinearLayoutManager $com.nauh.musicplayer.ui.MainActivity  List $com.nauh.musicplayer.ui.MainActivity  
MainPresenter $com.nauh.musicplayer.ui.MainActivity  MusicRepository $com.nauh.musicplayer.ui.MainActivity  PlayerActivity $com.nauh.musicplayer.ui.MainActivity  ProgressBar $com.nauh.musicplayer.ui.MainActivity  R $com.nauh.musicplayer.ui.MainActivity  RecyclerView $com.nauh.musicplayer.ui.MainActivity  
SearchView $com.nauh.musicplayer.ui.MainActivity  Song $com.nauh.musicplayer.ui.MainActivity  SongAdapter $com.nauh.musicplayer.ui.MainActivity  String $com.nauh.musicplayer.ui.MainActivity  SwipeRefreshLayout $com.nauh.musicplayer.ui.MainActivity  TextView $com.nauh.musicplayer.ui.MainActivity  Toast $com.nauh.musicplayer.ui.MainActivity  View $com.nauh.musicplayer.ui.MainActivity  apply $com.nauh.musicplayer.ui.MainActivity  emptyStateText $com.nauh.musicplayer.ui.MainActivity  findViewById $com.nauh.musicplayer.ui.MainActivity  initializePresenter $com.nauh.musicplayer.ui.MainActivity  initializeViews $com.nauh.musicplayer.ui.MainActivity  
isNullOrBlank $com.nauh.musicplayer.ui.MainActivity  java $com.nauh.musicplayer.ui.MainActivity  let $com.nauh.musicplayer.ui.MainActivity  	presenter $com.nauh.musicplayer.ui.MainActivity  progressBar $com.nauh.musicplayer.ui.MainActivity  recyclerView $com.nauh.musicplayer.ui.MainActivity  
searchView $com.nauh.musicplayer.ui.MainActivity  setContentView $com.nauh.musicplayer.ui.MainActivity  setupRecyclerView $com.nauh.musicplayer.ui.MainActivity  setupSearchView $com.nauh.musicplayer.ui.MainActivity  setupSwipeRefresh $com.nauh.musicplayer.ui.MainActivity  songAdapter $com.nauh.musicplayer.ui.MainActivity  
startActivity $com.nauh.musicplayer.ui.MainActivity  swipeRefreshLayout $com.nauh.musicplayer.ui.MainActivity  	ArrayList .com.nauh.musicplayer.ui.MainActivity.Companion  Intent .com.nauh.musicplayer.ui.MainActivity.Companion  LinearLayoutManager .com.nauh.musicplayer.ui.MainActivity.Companion  
MainPresenter .com.nauh.musicplayer.ui.MainActivity.Companion  MusicRepository .com.nauh.musicplayer.ui.MainActivity.Companion  PlayerActivity .com.nauh.musicplayer.ui.MainActivity.Companion  R .com.nauh.musicplayer.ui.MainActivity.Companion  SongAdapter .com.nauh.musicplayer.ui.MainActivity.Companion  Toast .com.nauh.musicplayer.ui.MainActivity.Companion  View .com.nauh.musicplayer.ui.MainActivity.Companion  apply .com.nauh.musicplayer.ui.MainActivity.Companion  
isNullOrBlank .com.nauh.musicplayer.ui.MainActivity.Companion  java .com.nauh.musicplayer.ui.MainActivity.Companion  let .com.nauh.musicplayer.ui.MainActivity.Companion  	presenter .com.nauh.musicplayer.ui.MainActivity.Companion  songAdapter .com.nauh.musicplayer.ui.MainActivity.Companion  OnQueryTextListener /com.nauh.musicplayer.ui.MainActivity.SearchView  View $com.nauh.musicplayer.ui.MainContract  Boolean &com.nauh.musicplayer.ui.PlayerActivity  Bundle &com.nauh.musicplayer.ui.PlayerActivity  	Companion &com.nauh.musicplayer.ui.PlayerActivity  EXTRA_PLAYLIST &com.nauh.musicplayer.ui.PlayerActivity  
EXTRA_SONG &com.nauh.musicplayer.ui.PlayerActivity  Glide &com.nauh.musicplayer.ui.PlayerActivity  ImageButton &com.nauh.musicplayer.ui.PlayerActivity  	ImageView &com.nauh.musicplayer.ui.PlayerActivity  Int &com.nauh.musicplayer.ui.PlayerActivity  List &com.nauh.musicplayer.ui.PlayerActivity  Long &com.nauh.musicplayer.ui.PlayerActivity  PlayerContract &com.nauh.musicplayer.ui.PlayerActivity  PlayerPresenter &com.nauh.musicplayer.ui.PlayerActivity  ProgressBar &com.nauh.musicplayer.ui.PlayerActivity  R &com.nauh.musicplayer.ui.PlayerActivity  RequestOptions &com.nauh.musicplayer.ui.PlayerActivity  RoundedCorners &com.nauh.musicplayer.ui.PlayerActivity  SeekBar &com.nauh.musicplayer.ui.PlayerActivity  Song &com.nauh.musicplayer.ui.PlayerActivity  String &com.nauh.musicplayer.ui.PlayerActivity  TextView &com.nauh.musicplayer.ui.PlayerActivity  Toast &com.nauh.musicplayer.ui.PlayerActivity  View &com.nauh.musicplayer.ui.PlayerActivity  albumArt &com.nauh.musicplayer.ui.PlayerActivity  
artistName &com.nauh.musicplayer.ui.PlayerActivity  
backButton &com.nauh.musicplayer.ui.PlayerActivity  currentTime &com.nauh.musicplayer.ui.PlayerActivity  	emptyList &com.nauh.musicplayer.ui.PlayerActivity  findViewById &com.nauh.musicplayer.ui.PlayerActivity  finish &com.nauh.musicplayer.ui.PlayerActivity  format &com.nauh.musicplayer.ui.PlayerActivity  
formatTime &com.nauh.musicplayer.ui.PlayerActivity  hideLoading &com.nauh.musicplayer.ui.PlayerActivity  initializePresenter &com.nauh.musicplayer.ui.PlayerActivity  initializeViews &com.nauh.musicplayer.ui.PlayerActivity  intent &com.nauh.musicplayer.ui.PlayerActivity  
isUserSeeking &com.nauh.musicplayer.ui.PlayerActivity  let &com.nauh.musicplayer.ui.PlayerActivity  
nextButton &com.nauh.musicplayer.ui.PlayerActivity  playPauseButton &com.nauh.musicplayer.ui.PlayerActivity  	presenter &com.nauh.musicplayer.ui.PlayerActivity  previousButton &com.nauh.musicplayer.ui.PlayerActivity  progressBar &com.nauh.musicplayer.ui.PlayerActivity  repeatButton &com.nauh.musicplayer.ui.PlayerActivity  seekBar &com.nauh.musicplayer.ui.PlayerActivity  setContentView &com.nauh.musicplayer.ui.PlayerActivity  setupClickListeners &com.nauh.musicplayer.ui.PlayerActivity  setupSeekBar &com.nauh.musicplayer.ui.PlayerActivity  	showError &com.nauh.musicplayer.ui.PlayerActivity  showLoading &com.nauh.musicplayer.ui.PlayerActivity  
shuffleButton &com.nauh.musicplayer.ui.PlayerActivity  	songTitle &com.nauh.musicplayer.ui.PlayerActivity  	totalTime &com.nauh.musicplayer.ui.PlayerActivity  EXTRA_PLAYLIST 0com.nauh.musicplayer.ui.PlayerActivity.Companion  
EXTRA_SONG 0com.nauh.musicplayer.ui.PlayerActivity.Companion  Glide 0com.nauh.musicplayer.ui.PlayerActivity.Companion  PlayerContract 0com.nauh.musicplayer.ui.PlayerActivity.Companion  PlayerPresenter 0com.nauh.musicplayer.ui.PlayerActivity.Companion  R 0com.nauh.musicplayer.ui.PlayerActivity.Companion  RequestOptions 0com.nauh.musicplayer.ui.PlayerActivity.Companion  RoundedCorners 0com.nauh.musicplayer.ui.PlayerActivity.Companion  String 0com.nauh.musicplayer.ui.PlayerActivity.Companion  Toast 0com.nauh.musicplayer.ui.PlayerActivity.Companion  View 0com.nauh.musicplayer.ui.PlayerActivity.Companion  currentTime 0com.nauh.musicplayer.ui.PlayerActivity.Companion  	emptyList 0com.nauh.musicplayer.ui.PlayerActivity.Companion  format 0com.nauh.musicplayer.ui.PlayerActivity.Companion  
formatTime 0com.nauh.musicplayer.ui.PlayerActivity.Companion  
isUserSeeking 0com.nauh.musicplayer.ui.PlayerActivity.Companion  let 0com.nauh.musicplayer.ui.PlayerActivity.Companion  	presenter 0com.nauh.musicplayer.ui.PlayerActivity.Companion  OnSeekBarChangeListener .com.nauh.musicplayer.ui.PlayerActivity.SeekBar  View &com.nauh.musicplayer.ui.PlayerContract  OnQueryTextListener "com.nauh.musicplayer.ui.SearchView  OnSeekBarChangeListener com.nauh.musicplayer.ui.SeekBar  Boolean com.nauh.musicplayer.ui.adapter  DiffUtil com.nauh.musicplayer.ui.adapter  Glide com.nauh.musicplayer.ui.adapter  	ImageView com.nauh.musicplayer.ui.adapter  Int com.nauh.musicplayer.ui.adapter  LayoutInflater com.nauh.musicplayer.ui.adapter  List com.nauh.musicplayer.ui.adapter  ListAdapter com.nauh.musicplayer.ui.adapter  R com.nauh.musicplayer.ui.adapter  RecyclerView com.nauh.musicplayer.ui.adapter  RequestOptions com.nauh.musicplayer.ui.adapter  RoundedCorners com.nauh.musicplayer.ui.adapter  Song com.nauh.musicplayer.ui.adapter  SongAdapter com.nauh.musicplayer.ui.adapter  SongViewHolder com.nauh.musicplayer.ui.adapter  String com.nauh.musicplayer.ui.adapter  TextView com.nauh.musicplayer.ui.adapter  Unit com.nauh.musicplayer.ui.adapter  View com.nauh.musicplayer.ui.adapter  	ViewGroup com.nauh.musicplayer.ui.adapter  android com.nauh.musicplayer.ui.adapter  currentList com.nauh.musicplayer.ui.adapter  indexOfFirst com.nauh.musicplayer.ui.adapter  mutableMapOf com.nauh.musicplayer.ui.adapter  onSongClick com.nauh.musicplayer.ui.adapter  set com.nauh.musicplayer.ui.adapter  ItemCallback (com.nauh.musicplayer.ui.adapter.DiffUtil  
ViewHolder ,com.nauh.musicplayer.ui.adapter.RecyclerView  Boolean +com.nauh.musicplayer.ui.adapter.SongAdapter  DiffUtil +com.nauh.musicplayer.ui.adapter.SongAdapter  Glide +com.nauh.musicplayer.ui.adapter.SongAdapter  	ImageView +com.nauh.musicplayer.ui.adapter.SongAdapter  Int +com.nauh.musicplayer.ui.adapter.SongAdapter  LayoutInflater +com.nauh.musicplayer.ui.adapter.SongAdapter  List +com.nauh.musicplayer.ui.adapter.SongAdapter  R +com.nauh.musicplayer.ui.adapter.SongAdapter  RecyclerView +com.nauh.musicplayer.ui.adapter.SongAdapter  RequestOptions +com.nauh.musicplayer.ui.adapter.SongAdapter  RoundedCorners +com.nauh.musicplayer.ui.adapter.SongAdapter  Song +com.nauh.musicplayer.ui.adapter.SongAdapter  SongDiffCallback +com.nauh.musicplayer.ui.adapter.SongAdapter  SongViewHolder +com.nauh.musicplayer.ui.adapter.SongAdapter  String +com.nauh.musicplayer.ui.adapter.SongAdapter  TextView +com.nauh.musicplayer.ui.adapter.SongAdapter  Unit +com.nauh.musicplayer.ui.adapter.SongAdapter  View +com.nauh.musicplayer.ui.adapter.SongAdapter  	ViewGroup +com.nauh.musicplayer.ui.adapter.SongAdapter  android +com.nauh.musicplayer.ui.adapter.SongAdapter  currentList +com.nauh.musicplayer.ui.adapter.SongAdapter  getItem +com.nauh.musicplayer.ui.adapter.SongAdapter  indexOfFirst +com.nauh.musicplayer.ui.adapter.SongAdapter  mutableMapOf +com.nauh.musicplayer.ui.adapter.SongAdapter  notifyDataSetChanged +com.nauh.musicplayer.ui.adapter.SongAdapter  notifyItemChanged +com.nauh.musicplayer.ui.adapter.SongAdapter  onSongClick +com.nauh.musicplayer.ui.adapter.SongAdapter  
playingStates +com.nauh.musicplayer.ui.adapter.SongAdapter  set +com.nauh.musicplayer.ui.adapter.SongAdapter  
submitList +com.nauh.musicplayer.ui.adapter.SongAdapter  updatePlayingState +com.nauh.musicplayer.ui.adapter.SongAdapter  ItemCallback 4com.nauh.musicplayer.ui.adapter.SongAdapter.DiffUtil  
ViewHolder 8com.nauh.musicplayer.ui.adapter.SongAdapter.RecyclerView  Glide :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  R :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  RequestOptions :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  RoundedCorners :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  View :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  albumArt :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  android :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  artist :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  bind :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  currentList :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  duration :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  itemView :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  onSongClick :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  playingIndicator :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  title :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  let java.lang.Runnable  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  	ArrayList 	java.util  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  Pair kotlin  Result kotlin  String kotlin  apply kotlin  let kotlin  map kotlin  run kotlin  takeIf kotlin  hashCode 
kotlin.Any  toString 
kotlin.Any  not kotlin.Boolean  toString kotlin.CharSequence  times 
kotlin.Double  toLong 
kotlin.Double  div kotlin.Float  times kotlin.Float  toInt kotlin.Float  invoke kotlin.Function2  	compareTo 
kotlin.Int  dec 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  takeIf 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  rem kotlin.Long  toFloat kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  let kotlin.Pair  	Companion 
kotlin.String  contains 
kotlin.String  format 
kotlin.String  hashCode 
kotlin.String  isBlank 
kotlin.String  
isNullOrBlank 
kotlin.String  let 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  List kotlin.collections  Map kotlin.collections  
MutableMap kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  find kotlin.collections  forEach kotlin.collections  indexOfFirst kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  mutableMapOf kotlin.collections  set kotlin.collections  sumOf kotlin.collections  	sumOfLong kotlin.collections  filter kotlin.collections.List  find kotlin.collections.List  get kotlin.collections.List  indexOfFirst kotlin.collections.List  
isNotEmpty kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  sumOf kotlin.collections.List  clear kotlin.collections.MutableMap  get kotlin.collections.MutableMap  set kotlin.collections.MutableMap  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus "kotlin.coroutines.CoroutineContext  java 
kotlin.jvm  contains 
kotlin.ranges  java kotlin.reflect.KClass  Sequence kotlin.sequences  contains kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  indexOfFirst kotlin.sequences  map kotlin.sequences  sumOf kotlin.sequences  contains kotlin.text  filter kotlin.text  find kotlin.text  forEach kotlin.text  format kotlin.text  indexOfFirst kotlin.text  isBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  map kotlin.text  set kotlin.text  sumOf kotlin.text  CompletableJob kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  Dispatchers !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  Song !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  contains !kotlinx.coroutines.CoroutineScope  currentSongs !kotlinx.coroutines.CoroutineScope  filter !kotlinx.coroutines.CoroutineScope  find !kotlinx.coroutines.CoroutineScope  getSongs !kotlinx.coroutines.CoroutineScope  isBlank !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  listOf !kotlinx.coroutines.CoroutineScope  musicRepository !kotlinx.coroutines.CoroutineScope  view !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  plus *kotlinx.coroutines.MainCoroutineDispatcher  	Parcelize kotlinx.parcelize  	Exception android.app.Activity  
parseTimeToMs android.app.Activity  split android.app.Activity  toLong android.app.Activity  	totalTime android.app.Activity  startForeground android.app.Service  stopForeground android.app.Service  	Exception android.content.Context  
parseTimeToMs android.content.Context  split android.content.Context  stopForeground android.content.Context  toLong android.content.Context  	totalTime android.content.Context  	Exception android.content.ContextWrapper  
parseTimeToMs android.content.ContextWrapper  split android.content.ContextWrapper  stopForeground android.content.ContextWrapper  toLong android.content.ContextWrapper  	totalTime android.content.ContextWrapper  	Exception  android.view.ContextThemeWrapper  
parseTimeToMs  android.view.ContextThemeWrapper  split  android.view.ContextThemeWrapper  toLong  android.view.ContextThemeWrapper  	totalTime  android.view.ContextThemeWrapper  	Exception #androidx.activity.ComponentActivity  
parseTimeToMs #androidx.activity.ComponentActivity  split #androidx.activity.ComponentActivity  toLong #androidx.activity.ComponentActivity  	totalTime #androidx.activity.ComponentActivity  
parseTimeToMs -androidx.activity.ComponentActivity.Companion  split -androidx.activity.ComponentActivity.Companion  toLong -androidx.activity.ComponentActivity.Companion  	totalTime -androidx.activity.ComponentActivity.Companion  	Exception (androidx.appcompat.app.AppCompatActivity  
parseTimeToMs (androidx.appcompat.app.AppCompatActivity  split (androidx.appcompat.app.AppCompatActivity  toLong (androidx.appcompat.app.AppCompatActivity  	totalTime (androidx.appcompat.app.AppCompatActivity  	Exception #androidx.core.app.ComponentActivity  
parseTimeToMs #androidx.core.app.ComponentActivity  split #androidx.core.app.ComponentActivity  toLong #androidx.core.app.ComponentActivity  	totalTime #androidx.core.app.ComponentActivity  	Exception &androidx.fragment.app.FragmentActivity  
parseTimeToMs &androidx.fragment.app.FragmentActivity  split &androidx.fragment.app.FragmentActivity  toLong &androidx.fragment.app.FragmentActivity  	totalTime &androidx.fragment.app.FragmentActivity  NotificationCompat androidx.media.app  LocalConfiguration  androidx.media3.common.MediaItem  let  androidx.media3.common.MediaItem  localConfiguration  androidx.media3.common.MediaItem  mediaId  androidx.media3.common.MediaItem  uri 3androidx.media3.common.MediaItem.LocalConfiguration  
albumTitle $androidx.media3.common.MediaMetadata  	isPlaying #androidx.media3.exoplayer.ExoPlayer  stopForeground +androidx.media3.session.MediaSessionService  MediaNotificationCompat com.nauh.musicplayer.service  stopForeground com.nauh.musicplayer.service  startForeground )com.nauh.musicplayer.service.MusicService  stopForeground )com.nauh.musicplayer.service.MusicService  Player 3com.nauh.musicplayer.service.MusicService.Companion  stopForeground 3com.nauh.musicplayer.service.MusicService.Companion  getCurrentPosition 3com.nauh.musicplayer.service.MusicServiceConnection  	isPlaying 3com.nauh.musicplayer.service.MusicServiceConnection  Song =com.nauh.musicplayer.service.MusicServiceConnection.Companion  onCurrentSongChanged Icom.nauh.musicplayer.service.MusicServiceConnection.PlaybackStateListener  onPlaybackError Icom.nauh.musicplayer.service.MusicServiceConnection.PlaybackStateListener  	Companion /com.nauh.musicplayer.service.NotificationHelper  MediaNotificationCompat /com.nauh.musicplayer.service.NotificationHelper  createNotificationForService /com.nauh.musicplayer.service.NotificationHelper  MediaNotificationCompat 9com.nauh.musicplayer.service.NotificationHelper.Companion  	Exception com.nauh.musicplayer.ui  
parseTimeToMs com.nauh.musicplayer.ui  split com.nauh.musicplayer.ui  toLong com.nauh.musicplayer.ui  	totalTime com.nauh.musicplayer.ui  	Exception &com.nauh.musicplayer.ui.PlayerActivity  
parseTimeToMs &com.nauh.musicplayer.ui.PlayerActivity  split &com.nauh.musicplayer.ui.PlayerActivity  toLong &com.nauh.musicplayer.ui.PlayerActivity  
parseTimeToMs 0com.nauh.musicplayer.ui.PlayerActivity.Companion  split 0com.nauh.musicplayer.ui.PlayerActivity.Companion  toLong 0com.nauh.musicplayer.ui.PlayerActivity.Companion  	totalTime 0com.nauh.musicplayer.ui.PlayerActivity.Companion  plus kotlin.Long  times kotlin.Long  split 
kotlin.String  toLong 
kotlin.String  split kotlin.text  toLong kotlin.text  CharSequence android.app.Activity  
ContextCompat android.app.Activity  Editable android.app.Activity  Suppress android.app.Activity  TextWatcher android.app.Activity  android android.app.Activity  androidx android.app.Activity  isEmpty android.app.Activity  to android.app.Activity  trim android.app.Activity  	appcompat android.app.Activity.androidx  widget 'android.app.Activity.androidx.appcompat  Toolbar .android.app.Activity.androidx.appcompat.widget  NotificationCompat android.app.NotificationChannel  Futures android.app.Service  map android.app.Service  
toMutableList android.app.Service  CharSequence android.content.Context  
ContextCompat android.content.Context  Editable android.content.Context  Futures android.content.Context  Suppress android.content.Context  TextWatcher android.content.Context  androidx android.content.Context  isEmpty android.content.Context  map android.content.Context  to android.content.Context  
toMutableList android.content.Context  trim android.content.Context  	appcompat  android.content.Context.androidx  widget *android.content.Context.androidx.appcompat  Toolbar 1android.content.Context.androidx.appcompat.widget  CharSequence android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  Editable android.content.ContextWrapper  Futures android.content.ContextWrapper  Suppress android.content.ContextWrapper  TextWatcher android.content.ContextWrapper  androidx android.content.ContextWrapper  isEmpty android.content.ContextWrapper  map android.content.ContextWrapper  to android.content.ContextWrapper  
toMutableList android.content.ContextWrapper  trim android.content.ContextWrapper  	appcompat 'android.content.ContextWrapper.androidx  widget 1android.content.ContextWrapper.androidx.appcompat  Toolbar 8android.content.ContextWrapper.androidx.appcompat.widget  FLAG_ACTIVITY_SINGLE_TOP android.content.Intent  Intent android.content.Intent  flags android.content.Intent  TIRAMISU android.os.Build.VERSION_CODES  Editable android.text  TextWatcher android.text  toString android.text.Editable  CharSequence  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  Editable  android.view.ContextThemeWrapper  Suppress  android.view.ContextThemeWrapper  TextWatcher  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  androidx  android.view.ContextThemeWrapper  isEmpty  android.view.ContextThemeWrapper  to  android.view.ContextThemeWrapper  trim  android.view.ContextThemeWrapper  	appcompat )android.view.ContextThemeWrapper.androidx  widget 3android.view.ContextThemeWrapper.androidx.appcompat  Toolbar :android.view.ContextThemeWrapper.androidx.appcompat.widget  EditText android.widget  LinearLayout android.widget  addTextChangedListener android.widget.EditText  setColorFilter android.widget.ImageButton  setColorFilter android.widget.ImageView  
visibility android.widget.LinearLayout  addTextChangedListener android.widget.TextView  LENGTH_SHORT android.widget.Toast  CharSequence #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  EditText #androidx.activity.ComponentActivity  Editable #androidx.activity.ComponentActivity  LinearLayout #androidx.activity.ComponentActivity  Suppress #androidx.activity.ComponentActivity  TextWatcher #androidx.activity.ComponentActivity  Toolbar #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  androidx #androidx.activity.ComponentActivity  isEmpty #androidx.activity.ComponentActivity  to #androidx.activity.ComponentActivity  trim #androidx.activity.ComponentActivity  
ContextCompat -androidx.activity.ComponentActivity.Companion  Song -androidx.activity.ComponentActivity.Companion  android -androidx.activity.ComponentActivity.Companion  isEmpty -androidx.activity.ComponentActivity.Companion  to -androidx.activity.ComponentActivity.Companion  trim -androidx.activity.ComponentActivity.Companion  	appcompat ,androidx.activity.ComponentActivity.androidx  widget 6androidx.activity.ComponentActivity.androidx.appcompat  Toolbar =androidx.activity.ComponentActivity.androidx.appcompat.widget  	ActionBar androidx.appcompat.app  setDisplayHomeAsUpEnabled  androidx.appcompat.app.ActionBar  CharSequence (androidx.appcompat.app.AppCompatActivity  
ContextCompat (androidx.appcompat.app.AppCompatActivity  Editable (androidx.appcompat.app.AppCompatActivity  Suppress (androidx.appcompat.app.AppCompatActivity  TextWatcher (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  androidx (androidx.appcompat.app.AppCompatActivity  isEmpty (androidx.appcompat.app.AppCompatActivity  setSupportActionBar (androidx.appcompat.app.AppCompatActivity  supportActionBar (androidx.appcompat.app.AppCompatActivity  to (androidx.appcompat.app.AppCompatActivity  trim (androidx.appcompat.app.AppCompatActivity  	appcompat 1androidx.appcompat.app.AppCompatActivity.androidx  widget ;androidx.appcompat.app.AppCompatActivity.androidx.appcompat  Toolbar Bandroidx.appcompat.app.AppCompatActivity.androidx.appcompat.widget  Toolbar androidx.appcompat.widget  setNavigationOnClickListener !androidx.appcompat.widget.Toolbar  NotificationManagerCompat androidx.core.app  CharSequence #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  EditText #androidx.core.app.ComponentActivity  Editable #androidx.core.app.ComponentActivity  LinearLayout #androidx.core.app.ComponentActivity  Suppress #androidx.core.app.ComponentActivity  TextWatcher #androidx.core.app.ComponentActivity  Toolbar #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  androidx #androidx.core.app.ComponentActivity  isEmpty #androidx.core.app.ComponentActivity  to #androidx.core.app.ComponentActivity  trim #androidx.core.app.ComponentActivity  	appcompat ,androidx.core.app.ComponentActivity.androidx  widget 6androidx.core.app.ComponentActivity.androidx.appcompat  Toolbar =androidx.core.app.ComponentActivity.androidx.appcompat.widget  CATEGORY_TRANSPORT $androidx.core.app.NotificationCompat  PRIORITY_LOW $androidx.core.app.NotificationCompat  setCategory ,androidx.core.app.NotificationCompat.Builder  setDeleteIntent ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setShowWhen ,androidx.core.app.NotificationCompat.Builder  
setSubText ,androidx.core.app.NotificationCompat.Builder  cancel +androidx.core.app.NotificationManagerCompat  from +androidx.core.app.NotificationManagerCompat  notify +androidx.core.app.NotificationManagerCompat  
ContextCompat androidx.core.content  getColor #androidx.core.content.ContextCompat  CharSequence &androidx.fragment.app.FragmentActivity  
ContextCompat &androidx.fragment.app.FragmentActivity  Editable &androidx.fragment.app.FragmentActivity  Suppress &androidx.fragment.app.FragmentActivity  TextWatcher &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  androidx &androidx.fragment.app.FragmentActivity  isEmpty &androidx.fragment.app.FragmentActivity  to &androidx.fragment.app.FragmentActivity  trim &androidx.fragment.app.FragmentActivity  	appcompat /androidx.fragment.app.FragmentActivity.androidx  widget 9androidx.fragment.app.FragmentActivity.androidx.appcompat  Toolbar @androidx.fragment.app.FragmentActivity.androidx.appcompat.widget  RequestMetadata  androidx.media3.common.MediaItem  	buildUpon  androidx.media3.common.MediaItem  requestMetadata  androidx.media3.common.MediaItem  mediaUri 0androidx.media3.common.MediaItem.RequestMetadata  Callback $androidx.media3.session.MediaSession  setCallback ,androidx.media3.session.MediaSession.Builder  Futures +androidx.media3.session.MediaSessionService  map +androidx.media3.session.MediaSessionService  
toMutableList +androidx.media3.session.MediaSessionService  let (androidx.recyclerview.widget.ListAdapter  onMoreOptionsClick (androidx.recyclerview.widget.ListAdapter  let 1androidx.recyclerview.widget.RecyclerView.Adapter  onMoreOptionsClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onMoreOptionsClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  error !com.bumptech.glide.RequestBuilder  placeholder !com.bumptech.glide.RequestBuilder  	transform !com.bumptech.glide.RequestBuilder  Futures !com.google.common.util.concurrent  immediateFuture )com.google.common.util.concurrent.Futures  primary com.nauh.musicplayer.R.color  text_secondary com.nauh.musicplayer.R.color  
ic_play_arrow com.nauh.musicplayer.R.drawable  albumArtwork com.nauh.musicplayer.R.id  artistAlbum com.nauh.musicplayer.R.id  currentTime com.nauh.musicplayer.R.id  emptyStateLayout com.nauh.musicplayer.R.id  
miniPlayer com.nauh.musicplayer.R.id  moreOptions com.nauh.musicplayer.R.id  
nextButton com.nauh.musicplayer.R.id  playPauseButton com.nauh.musicplayer.R.id  playerAlbumArtwork com.nauh.musicplayer.R.id  playerAlbumName com.nauh.musicplayer.R.id  playerArtistName com.nauh.musicplayer.R.id  playerProgressBar com.nauh.musicplayer.R.id  playerSongTitle com.nauh.musicplayer.R.id  
playerToolbar com.nauh.musicplayer.R.id  playingIndicator com.nauh.musicplayer.R.id  previousButton com.nauh.musicplayer.R.id  repeatButton com.nauh.musicplayer.R.id  searchEditText com.nauh.musicplayer.R.id  
shuffleButton com.nauh.musicplayer.R.id  songDuration com.nauh.musicplayer.R.id  	songTitle com.nauh.musicplayer.R.id  songsRecyclerView com.nauh.musicplayer.R.id  toolbar com.nauh.musicplayer.R.id  	totalTime com.nauh.musicplayer.R.id  clearSearchResults /com.nauh.musicplayer.contract.MainContract.View  showSearchResults /com.nauh.musicplayer.contract.MainContract.View  updateCurrentPlayingSong /com.nauh.musicplayer.contract.MainContract.View  GET com.nauh.musicplayer.data.api  List com.nauh.musicplayer.data.api  
MockMusicData com.nauh.musicplayer.data.api  MusicApiService com.nauh.musicplayer.data.api  Path com.nauh.musicplayer.data.api  Response com.nauh.musicplayer.data.api  Song com.nauh.musicplayer.data.api  String com.nauh.musicplayer.data.api  listOf com.nauh.musicplayer.data.api  Song +com.nauh.musicplayer.data.api.MockMusicData  getSampleSongs +com.nauh.musicplayer.data.api.MockMusicData  listOf +com.nauh.musicplayer.data.api.MockMusicData  genre $com.nauh.musicplayer.data.model.Song  getArtistAlbumText $com.nauh.musicplayer.data.model.Song  	Exception $com.nauh.musicplayer.data.repository  
MockMusicData $com.nauh.musicplayer.data.repository  MusicApiService $com.nauh.musicplayer.data.repository  Result $com.nauh.musicplayer.data.repository  Volatile $com.nauh.musicplayer.data.repository  also $com.nauh.musicplayer.data.repository  delay $com.nauh.musicplayer.data.repository  equals $com.nauh.musicplayer.data.repository  failure $com.nauh.musicplayer.data.repository  getSampleSongs $com.nauh.musicplayer.data.repository  success $com.nauh.musicplayer.data.repository  synchronized $com.nauh.musicplayer.data.repository  	Companion 4com.nauh.musicplayer.data.repository.MusicRepository  	Exception 4com.nauh.musicplayer.data.repository.MusicRepository  INSTANCE 4com.nauh.musicplayer.data.repository.MusicRepository  List 4com.nauh.musicplayer.data.repository.MusicRepository  
MockMusicData 4com.nauh.musicplayer.data.repository.MusicRepository  MusicApiService 4com.nauh.musicplayer.data.repository.MusicRepository  MusicRepository 4com.nauh.musicplayer.data.repository.MusicRepository  Result 4com.nauh.musicplayer.data.repository.MusicRepository  String 4com.nauh.musicplayer.data.repository.MusicRepository  Volatile 4com.nauh.musicplayer.data.repository.MusicRepository  also 4com.nauh.musicplayer.data.repository.MusicRepository  delay 4com.nauh.musicplayer.data.repository.MusicRepository  equals 4com.nauh.musicplayer.data.repository.MusicRepository  failure 4com.nauh.musicplayer.data.repository.MusicRepository  getAllSongs 4com.nauh.musicplayer.data.repository.MusicRepository  getInstance 4com.nauh.musicplayer.data.repository.MusicRepository  getSampleSongs 4com.nauh.musicplayer.data.repository.MusicRepository  success 4com.nauh.musicplayer.data.repository.MusicRepository  synchronized 4com.nauh.musicplayer.data.repository.MusicRepository  Dispatchers >com.nauh.musicplayer.data.repository.MusicRepository.Companion  INSTANCE >com.nauh.musicplayer.data.repository.MusicRepository.Companion  
MockMusicData >com.nauh.musicplayer.data.repository.MusicRepository.Companion  MusicRepository >com.nauh.musicplayer.data.repository.MusicRepository.Companion  Result >com.nauh.musicplayer.data.repository.MusicRepository.Companion  also >com.nauh.musicplayer.data.repository.MusicRepository.Companion  contains >com.nauh.musicplayer.data.repository.MusicRepository.Companion  delay >com.nauh.musicplayer.data.repository.MusicRepository.Companion  equals >com.nauh.musicplayer.data.repository.MusicRepository.Companion  failure >com.nauh.musicplayer.data.repository.MusicRepository.Companion  filter >com.nauh.musicplayer.data.repository.MusicRepository.Companion  find >com.nauh.musicplayer.data.repository.MusicRepository.Companion  getInstance >com.nauh.musicplayer.data.repository.MusicRepository.Companion  getSampleSongs >com.nauh.musicplayer.data.repository.MusicRepository.Companion  success >com.nauh.musicplayer.data.repository.MusicRepository.Companion  synchronized >com.nauh.musicplayer.data.repository.MusicRepository.Companion  withContext >com.nauh.musicplayer.data.repository.MusicRepository.Companion  fold com.nauh.musicplayer.presenter  getInstance com.nauh.musicplayer.presenter  isBlank com.nauh.musicplayer.presenter  random com.nauh.musicplayer.presenter  
repository com.nauh.musicplayer.presenter  until com.nauh.musicplayer.presenter  clearSearch ,com.nauh.musicplayer.presenter.MainPresenter  fold ,com.nauh.musicplayer.presenter.MainPresenter  isBlank ,com.nauh.musicplayer.presenter.MainPresenter  
repository ,com.nauh.musicplayer.presenter.MainPresenter  	searchJob ,com.nauh.musicplayer.presenter.MainPresenter  playlist .com.nauh.musicplayer.presenter.PlayerPresenter  random .com.nauh.musicplayer.presenter.PlayerPresenter  until .com.nauh.musicplayer.presenter.PlayerPresenter  ACTION_PLAY_PAUSE com.nauh.musicplayer.service  ACTION_STOP com.nauh.musicplayer.service  Futures com.nauh.musicplayer.service  MutableList com.nauh.musicplayer.service  NotificationManagerCompat com.nauh.musicplayer.service  
toMutableList com.nauh.musicplayer.service  Callback )com.nauh.musicplayer.service.MediaSession  Futures )com.nauh.musicplayer.service.MusicService  List )com.nauh.musicplayer.service.MusicService  ListenableFuture )com.nauh.musicplayer.service.MusicService  MediaSessionCallback )com.nauh.musicplayer.service.MusicService  MutableList )com.nauh.musicplayer.service.MusicService  map )com.nauh.musicplayer.service.MusicService  
toMutableList )com.nauh.musicplayer.service.MusicService  Futures 3com.nauh.musicplayer.service.MusicService.Companion  map 3com.nauh.musicplayer.service.MusicService.Companion  
toMutableList 3com.nauh.musicplayer.service.MusicService.Companion  Callback 6com.nauh.musicplayer.service.MusicService.MediaSession  Futures >com.nauh.musicplayer.service.MusicService.MediaSessionCallback  map >com.nauh.musicplayer.service.MusicService.MediaSessionCallback  
toMutableList >com.nauh.musicplayer.service.MusicService.MediaSessionCallback  ACTION_NEXT /com.nauh.musicplayer.service.NotificationHelper  ACTION_PLAY_PAUSE /com.nauh.musicplayer.service.NotificationHelper  ACTION_PREVIOUS /com.nauh.musicplayer.service.NotificationHelper  ACTION_STOP /com.nauh.musicplayer.service.NotificationHelper  NotificationManagerCompat /com.nauh.musicplayer.service.NotificationHelper  Song /com.nauh.musicplayer.service.NotificationHelper  android /com.nauh.musicplayer.service.NotificationHelper  createActionIntent /com.nauh.musicplayer.service.NotificationHelper  ACTION_NEXT 9com.nauh.musicplayer.service.NotificationHelper.Companion  ACTION_PLAY_PAUSE 9com.nauh.musicplayer.service.NotificationHelper.Companion  ACTION_PREVIOUS 9com.nauh.musicplayer.service.NotificationHelper.Companion  ACTION_STOP 9com.nauh.musicplayer.service.NotificationHelper.Companion  NotificationManagerCompat 9com.nauh.musicplayer.service.NotificationHelper.Companion  app 7com.nauh.musicplayer.service.NotificationHelper.android  Notification ;com.nauh.musicplayer.service.NotificationHelper.android.app  app $com.nauh.musicplayer.service.android  Notification (com.nauh.musicplayer.service.android.app  CharSequence com.nauh.musicplayer.ui  
ContextCompat com.nauh.musicplayer.ui  EditText com.nauh.musicplayer.ui  Editable com.nauh.musicplayer.ui  LinearLayout com.nauh.musicplayer.ui  Suppress com.nauh.musicplayer.ui  TextWatcher com.nauh.musicplayer.ui  Toolbar com.nauh.musicplayer.ui  android com.nauh.musicplayer.ui  androidx com.nauh.musicplayer.ui  isEmpty com.nauh.musicplayer.ui  to com.nauh.musicplayer.ui  trim com.nauh.musicplayer.ui  emptyStateLayout $com.nauh.musicplayer.ui.MainActivity  hideLoading $com.nauh.musicplayer.ui.MainActivity  isEmpty $com.nauh.musicplayer.ui.MainActivity  
miniPlayer $com.nauh.musicplayer.ui.MainActivity  searchEditText $com.nauh.musicplayer.ui.MainActivity  setSupportActionBar $com.nauh.musicplayer.ui.MainActivity  showSongOptions $com.nauh.musicplayer.ui.MainActivity  	showSongs $com.nauh.musicplayer.ui.MainActivity  songsRecyclerView $com.nauh.musicplayer.ui.MainActivity  trim $com.nauh.musicplayer.ui.MainActivity  
ContextCompat &com.nauh.musicplayer.ui.PlayerActivity  Suppress &com.nauh.musicplayer.ui.PlayerActivity  Toolbar &com.nauh.musicplayer.ui.PlayerActivity  albumArtwork &com.nauh.musicplayer.ui.PlayerActivity  	albumName &com.nauh.musicplayer.ui.PlayerActivity  android &com.nauh.musicplayer.ui.PlayerActivity  java &com.nauh.musicplayer.ui.PlayerActivity  setSupportActionBar &com.nauh.musicplayer.ui.PlayerActivity  setupToolbar &com.nauh.musicplayer.ui.PlayerActivity  supportActionBar &com.nauh.musicplayer.ui.PlayerActivity  to &com.nauh.musicplayer.ui.PlayerActivity  toolbar &com.nauh.musicplayer.ui.PlayerActivity  
ContextCompat 0com.nauh.musicplayer.ui.PlayerActivity.Companion  Song 0com.nauh.musicplayer.ui.PlayerActivity.Companion  android 0com.nauh.musicplayer.ui.PlayerActivity.Companion  java 0com.nauh.musicplayer.ui.PlayerActivity.Companion  to 0com.nauh.musicplayer.ui.PlayerActivity.Companion  ImageButton com.nauh.musicplayer.ui.adapter  let com.nauh.musicplayer.ui.adapter  onMoreOptionsClick com.nauh.musicplayer.ui.adapter  ImageButton +com.nauh.musicplayer.ui.adapter.SongAdapter  currentPlayingSong +com.nauh.musicplayer.ui.adapter.SongAdapter  let +com.nauh.musicplayer.ui.adapter.SongAdapter  onMoreOptionsClick +com.nauh.musicplayer.ui.adapter.SongAdapter  updateCurrentPlayingSong +com.nauh.musicplayer.ui.adapter.SongAdapter  albumArtwork :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  artistAlbum :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  moreOptions :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  onMoreOptionsClick :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  songDuration :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  	songTitle :com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder  	appcompat  com.nauh.musicplayer.ui.androidx  widget *com.nauh.musicplayer.ui.androidx.appcompat  Toolbar 1com.nauh.musicplayer.ui.androidx.appcompat.widget  Suppress kotlin  	Throwable kotlin  also kotlin  fold kotlin  synchronized kotlin  to kotlin  equals 
kotlin.Any  isEmpty kotlin.CharSequence  toLong kotlin.Float  invoke kotlin.Function1  plus 
kotlin.Int  to 
kotlin.Int  toFloat 
kotlin.Int  	Companion 
kotlin.Result  failure 
kotlin.Result  fold 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  equals 
kotlin.String  isEmpty 
kotlin.String  trim 
kotlin.String  MutableList kotlin.collections  fold kotlin.collections  isEmpty kotlin.collections  random kotlin.collections  
toMutableList kotlin.collections  indexOf kotlin.collections.List  isEmpty kotlin.collections.List  
toMutableList kotlin.collections.List  map kotlin.collections.MutableList  Volatile 
kotlin.jvm  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  random 
kotlin.ranges  until 
kotlin.ranges  random kotlin.ranges.IntRange  fold kotlin.sequences  
toMutableList kotlin.sequences  equals kotlin.text  fold kotlin.text  isEmpty kotlin.text  random kotlin.text  
toMutableList kotlin.text  trim kotlin.text  Delay kotlinx.coroutines  delay kotlinx.coroutines  
MockMusicData !kotlinx.coroutines.CoroutineScope  Result !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  equals !kotlinx.coroutines.CoroutineScope  failure !kotlinx.coroutines.CoroutineScope  fold !kotlinx.coroutines.CoroutineScope  getSampleSongs !kotlinx.coroutines.CoroutineScope  
repository !kotlinx.coroutines.CoroutineScope  success !kotlinx.coroutines.CoroutineScope  cancel kotlinx.coroutines.Job  Response 	retrofit2  GET retrofit2.http  Path retrofit2.http  
onSongChanged com.nauh.musicplayer.presenter  initializeMusicService .com.nauh.musicplayer.presenter.PlayerPresenter  
onSongChanged 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  view 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  PlaybackStateListener Ecom.nauh.musicplayer.presenter.PlayerPresenter.MusicServiceConnection  
onSongChanged 3com.nauh.musicplayer.service.MusicServiceConnection  view 3com.nauh.musicplayer.service.MusicServiceConnection  Handler com.nauh.musicplayer.presenter  Looper com.nauh.musicplayer.presenter  Handler .com.nauh.musicplayer.presenter.PlayerPresenter  Looper .com.nauh.musicplayer.presenter.PlayerPresenter  Handler 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  Looper 8com.nauh.musicplayer.presenter.PlayerPresenter.Companion  P android.os.Build.VERSION_CODES  RequiresApi androidx.annotation  RequiresApi com.nauh.musicplayer.service  Build 3com.nauh.musicplayer.service.MusicServiceConnection  RequiresApi 3com.nauh.musicplayer.service.MusicServiceConnection  Build =com.nauh.musicplayer.service.MusicServiceConnection.Companion  PlaybackException android.app.Service  player android.app.Service  PlaybackException android.content.Context  player android.content.Context  PlaybackException android.content.ContextWrapper  player android.content.ContextWrapper  PlaybackException androidx.media3.common  	errorCode (androidx.media3.common.PlaybackException  message (androidx.media3.common.PlaybackException  currentMediaItem #androidx.media3.exoplayer.ExoPlayer  PlaybackException +androidx.media3.session.MediaSessionService  player +androidx.media3.session.MediaSessionService  PlaybackException com.nauh.musicplayer.service  player com.nauh.musicplayer.service  PlaybackException )com.nauh.musicplayer.service.MusicService  player 3com.nauh.musicplayer.service.MusicService.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        