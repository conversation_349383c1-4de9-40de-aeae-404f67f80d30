{"logs": [{"outputFile": "com.nauh.musicplayer.app-mergeDebugResources-40:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8c6e034149c65399529a9cd0f184f8b\\transformed\\media3-session-1.2.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,140,214,285,354,435,520,610", "endColumns": "84,73,70,68,80,84,89,93", "endOffsets": "135,209,280,349,430,515,605,699"}, "to": {"startLines": "50,66,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3648,5266,10491,10562,10631,10712,10797,10887", "endColumns": "84,73,70,68,80,84,89,93", "endOffsets": "3728,5335,10557,10626,10707,10792,10882,10976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87c47c7abe7f29a54b096c7ff4d96851\\transformed\\core-1.16.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,569,673,784", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "150,252,355,462,564,668,779,880"}, "to": {"startLines": "56,57,58,59,60,61,62,185", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4211,4311,4413,4516,4623,4725,4829,14599", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "4306,4408,4511,4618,4720,4824,4935,14695"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e355fbe8fe59cc5407000c654ab4b66\\transformed\\appcompat-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "936,1047,1156,1268,1353,1458,1575,1654,1732,1823,1916,2011,2105,2205,2298,2393,2488,2579,2670,2751,2857,2962,3060,3167,3270,3385,3546,14517", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "1042,1151,1263,1348,1453,1570,1649,1727,1818,1911,2006,2100,2200,2293,2388,2483,2574,2665,2746,2852,2957,3055,3162,3265,3380,3541,3643,14594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c077dfbc8dc20562e970eb684d92a281\\transformed\\media3-ui-1.2.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,484,676,768,858,936,1026,1123,1210,1276,1373,1471,1539,1605,1670,1740,1871,2000,2136,2208,2289,2363,2451,2545,2636,2703,2769,2822,2883,2931,2992,3065,3141,3201,3271,3329,3386,3452,3517,3583,3635,3694,3770,3846", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,89,77,89,96,86,65,96,97,67,65,64,69,130,128,135,71,80,73,87,93,90,66,65,52,60,47,60,72,75,59,69,57,56,65,64,65,51,58,75,75,54", "endOffsets": "280,479,671,763,853,931,1021,1118,1205,1271,1368,1466,1534,1600,1665,1735,1866,1995,2131,2203,2284,2358,2446,2540,2631,2698,2764,2817,2878,2926,2987,3060,3136,3196,3266,3324,3381,3447,3512,3578,3630,3689,3765,3841,3896"}, "to": {"startLines": "2,11,15,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,579,5404,5496,5586,5664,5754,5851,5938,6004,6101,6199,6267,6333,6398,6468,6599,6728,6864,6936,7017,7091,7179,7273,7364,7431,8185,8238,8299,8347,8408,8481,8557,8617,8687,8745,8802,8868,8933,8999,9051,9110,9186,9262", "endLines": "10,14,18,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "endColumns": "17,12,12,91,89,77,89,96,86,65,96,97,67,65,64,69,130,128,135,71,80,73,87,93,90,66,65,52,60,47,60,72,75,59,69,57,56,65,64,65,51,58,75,75,54", "endOffsets": "375,574,766,5491,5581,5659,5749,5846,5933,5999,6096,6194,6262,6328,6393,6463,6594,6723,6859,6931,7012,7086,7174,7268,7359,7426,7492,8233,8294,8342,8403,8476,8552,8612,8682,8740,8797,8863,8928,8994,9046,9105,9181,9257,9312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d41d36b14e47c257e85459df73d97ebd\\transformed\\media3-exoplayer-1.2.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,262,333,420,491,578,662", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "121,186,257,328,415,486,573,657,738"}, "to": {"startLines": "92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7497,7568,7633,7704,7775,7862,7933,8020,8104", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "7563,7628,7699,7770,7857,7928,8015,8099,8180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c6ed593d095e40b444f65ff3ae72d38a\\transformed\\material-1.10.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,633,743,844,985,1069,1133,1227,1297,1358,1445,1508,1572,1631,1705,1767,1821,1938,1996,2057,2111,2185,2307,2391,2487,2619,2697,2775,2904,2993,3073,3134,3189,3255,3324,3401,3488,3569,3643,3719,3809,3882,3984,4069,4148,4238,4330,4404,4489,4579,4631,4715,4780,4865,4950,5012,5076,5139,5208,5325,5433,5533,5637,5702,5761", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,82,84,84,114,109,100,140,83,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,95,131,77,77,128,88,79,60,54,65,68,76,86,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81", "endOffsets": "260,343,428,513,628,738,839,980,1064,1128,1222,1292,1353,1440,1503,1567,1626,1700,1762,1816,1933,1991,2052,2106,2180,2302,2386,2482,2614,2692,2770,2899,2988,3068,3129,3184,3250,3319,3396,3483,3564,3638,3714,3804,3877,3979,4064,4143,4233,4325,4399,4484,4574,4626,4710,4775,4860,4945,5007,5071,5134,5203,5320,5428,5528,5632,5697,5756,5838"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,67,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "771,3733,3816,3901,3986,4101,4940,5041,5182,5340,9317,9411,9481,9542,9629,9692,9756,9815,9889,9951,10005,10122,10180,10241,10295,10369,10981,11065,11161,11293,11371,11449,11578,11667,11747,11808,11863,11929,11998,12075,12162,12243,12317,12393,12483,12556,12658,12743,12822,12912,13004,13078,13163,13253,13305,13389,13454,13539,13624,13686,13750,13813,13882,13999,14107,14207,14311,14376,14435", "endLines": "22,51,52,53,54,55,63,64,65,67,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "endColumns": "12,82,84,84,114,109,100,140,83,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,95,131,77,77,128,88,79,60,54,65,68,76,86,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81", "endOffsets": "931,3811,3896,3981,4096,4206,5036,5177,5261,5399,9406,9476,9537,9624,9687,9751,9810,9884,9946,10000,10117,10175,10236,10290,10364,10486,11060,11156,11288,11366,11444,11573,11662,11742,11803,11858,11924,11993,12070,12157,12238,12312,12388,12478,12551,12653,12738,12817,12907,12999,13073,13158,13248,13300,13384,13449,13534,13619,13681,13745,13808,13877,13994,14102,14202,14306,14371,14430,14512"}}]}]}