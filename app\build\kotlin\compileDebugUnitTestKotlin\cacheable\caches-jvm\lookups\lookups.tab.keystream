  ExampleUnitTest com.nauh.musicplayer  Song com.nauh.musicplayer  
SongModelTest com.nauh.musicplayer  Test com.nauh.musicplayer  assertEquals com.nauh.musicplayer  assertEquals $com.nauh.musicplayer.ExampleUnitTest  Song "com.nauh.musicplayer.SongModelTest  assertEquals "com.nauh.musicplayer.SongModelTest  Song com.nauh.musicplayer.data.model  getArtistAlbumText $com.nauh.musicplayer.data.model.Song  getFormattedDuration $com.nauh.musicplayer.data.model.Song  plus 
kotlin.Int  Song 	org.junit  Test 	org.junit  assertEquals 	org.junit  assertEquals org.junit.Assert  
getMediaId $com.nauh.musicplayer.data.model.Song  Before com.nauh.musicplayer  Boolean com.nauh.musicplayer  Int com.nauh.musicplayer  List com.nauh.musicplayer  Long com.nauh.musicplayer  PlayerContract com.nauh.musicplayer  PlayerPresenter com.nauh.musicplayer  PlayerPresenterTest com.nauh.musicplayer  String com.nauh.musicplayer  
assertTrue com.nauh.musicplayer  listOf com.nauh.musicplayer  
mutableListOf com.nauh.musicplayer  
viewCallbacks com.nauh.musicplayer  View #com.nauh.musicplayer.PlayerContract  PlayerContract (com.nauh.musicplayer.PlayerPresenterTest  PlayerPresenter (com.nauh.musicplayer.PlayerPresenterTest  Song (com.nauh.musicplayer.PlayerPresenterTest  
assertTrue (com.nauh.musicplayer.PlayerPresenterTest  listOf (com.nauh.musicplayer.PlayerPresenterTest  mockView (com.nauh.musicplayer.PlayerPresenterTest  
mutableListOf (com.nauh.musicplayer.PlayerPresenterTest  	presenter (com.nauh.musicplayer.PlayerPresenterTest  testPlaylist (com.nauh.musicplayer.PlayerPresenterTest  testSong (com.nauh.musicplayer.PlayerPresenterTest  
viewCallbacks (com.nauh.musicplayer.PlayerPresenterTest  PlayerContract com.nauh.musicplayer.contract  
RepeatMode ,com.nauh.musicplayer.contract.PlayerContract  View ,com.nauh.musicplayer.contract.PlayerContract  ALL 7com.nauh.musicplayer.contract.PlayerContract.RepeatMode  OFF 7com.nauh.musicplayer.contract.PlayerContract.RepeatMode  ONE 7com.nauh.musicplayer.contract.PlayerContract.RepeatMode  title $com.nauh.musicplayer.data.model.Song  PlayerPresenter com.nauh.musicplayer.presenter  
attachView .com.nauh.musicplayer.presenter.PlayerPresenter  initializePlayer .com.nauh.musicplayer.presenter.PlayerPresenter  onPlaybackStateChanged .com.nauh.musicplayer.presenter.PlayerPresenter  onProgressUpdate .com.nauh.musicplayer.presenter.PlayerPresenter  
onSongChanged .com.nauh.musicplayer.presenter.PlayerPresenter  toggleRepeat .com.nauh.musicplayer.presenter.PlayerPresenter  
toggleShuffle .com.nauh.musicplayer.presenter.PlayerPresenter  List kotlin.collections  MutableList kotlin.collections  listOf kotlin.collections  
mutableListOf kotlin.collections  size kotlin.collections.List  add kotlin.collections.MutableList  clear kotlin.collections.MutableList  contains kotlin.collections.MutableList  Before 	org.junit  Boolean 	org.junit  Int 	org.junit  List 	org.junit  Long 	org.junit  PlayerContract 	org.junit  PlayerPresenter 	org.junit  String 	org.junit  
assertTrue 	org.junit  listOf 	org.junit  
mutableListOf 	org.junit  
viewCallbacks 	org.junit  
assertTrue org.junit.Assert  View org.junit.PlayerContract  Uri android.net  toString android.net.Uri  	MediaItem androidx.media3.common  
MediaMetadata androidx.media3.common  LocalConfiguration  androidx.media3.common.MediaItem  localConfiguration  androidx.media3.common.MediaItem  mediaId  androidx.media3.common.MediaItem  
mediaMetadata  androidx.media3.common.MediaItem  uri 3androidx.media3.common.MediaItem.LocalConfiguration  
albumTitle $androidx.media3.common.MediaMetadata  artist $androidx.media3.common.MediaMetadata  title $androidx.media3.common.MediaMetadata  IllegalArgumentException com.nauh.musicplayer  MusicService com.nauh.musicplayer  MusicServiceTest com.nauh.musicplayer  createMediaItem com.nauh.musicplayer  toString com.nauh.musicplayer  IllegalArgumentException %com.nauh.musicplayer.MusicServiceTest  MusicService %com.nauh.musicplayer.MusicServiceTest  Song %com.nauh.musicplayer.MusicServiceTest  assertEquals %com.nauh.musicplayer.MusicServiceTest  createMediaItem %com.nauh.musicplayer.MusicServiceTest  toString %com.nauh.musicplayer.MusicServiceTest  album $com.nauh.musicplayer.data.model.Song  artist $com.nauh.musicplayer.data.model.Song  id $com.nauh.musicplayer.data.model.Song  	streamUrl $com.nauh.musicplayer.data.model.Song  MusicService com.nauh.musicplayer.service  	Companion )com.nauh.musicplayer.service.MusicService  createMediaItem )com.nauh.musicplayer.service.MusicService  createMediaItem 3com.nauh.musicplayer.service.MusicService.Companion  CharSequence kotlin  IllegalArgumentException kotlin  toString kotlin  toString 
kotlin.Any  toString kotlin.CharSequence  toString kotlin.collections  toString kotlin.text  IllegalArgumentException 	org.junit  MusicService 	org.junit  createMediaItem 	org.junit  toString 	org.junit                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      