<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="MissingSuperCall"
        severity="error"
        message="Overriding method should call `super.onStartCommand`">
        <fix-data/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/nauh/musicplayer/service/MusicService.kt"
            line="148"
            column="18"
            startOffset="5004"
            endLine="148"
            endColumn="32"
            endOffset="5018"/>
    </incident>

    <incident
        id="ResAuto"
        severity="fatal"
        message="In Gradle projects, always use `http://schemas.android.com/apk/res-auto` for custom attributes">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/mini_player.xml"
            line="3"
            column="16"
            startOffset="127"
            endLine="3"
            endColumn="55"
            endOffset="166"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/nauh/musicplayer/ui/PlayerActivity.kt"
            line="233"
            column="16"
            startOffset="8148"
            endLine="233"
            endColumn="60"
            endOffset="8192"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/nauh/musicplayer/data/model/Playlist.kt"
            line="36"
            column="13"
            startOffset="949"
            endLine="36"
            endColumn="69"
            endOffset="1005"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/nauh/musicplayer/data/model/Playlist.kt"
            line="38"
            column="13"
            startOffset="1035"
            endLine="38"
            endColumn="57"
            endOffset="1079"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/nauh/musicplayer/data/model/Song.kt"
            line="29"
            column="16"
            startOffset="748"
            endLine="29"
            endColumn="60"
            endOffset="792"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.10.1 is available: 8.11.1">
        <fix-replace
            description="Change to 8.11.1"
            family="Update versions"
            oldString="8.10.1"
            replacement="8.11.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="15"
            endOffset="25"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-exoplayer than 1.2.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.2.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="48"
            column="20"
            startOffset="1154"
            endLine="48"
            endColumn="60"
            endOffset="1194"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-ui than 1.2.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.2.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="49"
            column="20"
            startOffset="1215"
            endLine="49"
            endColumn="53"
            endOffset="1248"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-session than 1.2.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.2.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="50"
            column="20"
            startOffset="1269"
            endLine="50"
            endColumn="58"
            endOffset="1307"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media3:media3-common than 1.2.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.2.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="51"
            column="20"
            startOffset="1328"
            endLine="51"
            endColumn="57"
            endOffset="1365"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.media:media than 1.6.0 is available: 1.7.0">
        <fix-replace
            description="Change to 1.7.0"
            family="Update versions"
            oldString="1.6.0"
            replacement="1.7.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="54"
            column="20"
            startOffset="1426"
            endLine="54"
            endColumn="48"
            endOffset="1454"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.guava:guava than 31.1-android is available: 33.3.1-android">
        <fix-replace
            description="Change to 33.3.1-android"
            family="Update versions"
            oldString="31.1-android"
            replacement="33.3.1-android"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="57"
            column="20"
            startOffset="1510"
            endLine="57"
            endColumn="57"
            endOffset="1547"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.squareup.retrofit2:retrofit than 2.9.0 is available: 3.0.0">
        <fix-replace
            description="Change to 3.0.0"
            family="Update versions"
            oldString="2.9.0"
            replacement="3.0.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="60"
            column="20"
            startOffset="1587"
            endLine="60"
            endColumn="59"
            endOffset="1626"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.squareup.retrofit2:converter-gson than 2.9.0 is available: 3.0.0">
        <fix-replace
            description="Change to 3.0.0"
            family="Update versions"
            oldString="2.9.0"
            replacement="3.0.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="61"
            column="20"
            startOffset="1647"
            endLine="61"
            endColumn="65"
            endOffset="1692"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.recyclerview:recyclerview than 1.3.2 is available: 1.4.0">
        <fix-replace
            description="Change to 1.4.0"
            family="Update versions"
            oldString="1.3.2"
            replacement="1.4.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="71"
            column="20"
            startOffset="1971"
            endLine="71"
            endColumn="62"
            endOffset="2013"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.2">
        <fix-replace
            description="Change to 2.9.2"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.2"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="77"
            column="20"
            startOffset="2165"
            endLine="77"
            endColumn="70"
            endOffset="2215"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.2">
        <fix-replace
            description="Change to 2.9.2"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.2"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="78"
            column="20"
            startOffset="2236"
            endLine="78"
            endColumn="69"
            endOffset="2285"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of org.jetbrains.kotlinx:kotlinx-coroutines-android than 1.7.3 is available: 1.8.1">
        <fix-replace
            description="Change to 1.8.1"
            family="Update versions"
            oldString="1.7.3"
            replacement="1.8.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="81"
            column="20"
            startOffset="2325"
            endLine="81"
            endColumn="76"
            endOffset="2381"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.2.0">
        <fix-replace
            description="Change to 2.2.0"
            family="Update versions"
            oldString="2.0.21"
            replacement="2.2.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="35"
            endLine="3"
            endColumn="18"
            endOffset="43"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.6.1"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="8"
            column="13"
            startOffset="138"
            endLine="8"
            endColumn="20"
            endOffset="145"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0">
        <fix-replace
            description="Change to 1.12.0"
            family="Update versions"
            oldString="1.10.0"
            replacement="1.12.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="9"
            column="12"
            startOffset="157"
            endLine="9"
            endColumn="20"
            endOffset="165"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1">
        <fix-replace
            description="Change to 2.2.1"
            family="Update versions"
            oldString="2.1.4"
            replacement="2.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="20"
            startOffset="205"
            endLine="11"
            endColumn="27"
            endOffset="212"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="40"
            column="13"
            startOffset="1817"
            endLine="40"
            endColumn="49"
            endOffset="1853"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;@color/colorPrimary&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;@color/colorPrimary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/colorPrimary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_song.xml"
            line="75"
            column="9"
            startOffset="2587"
            endLine="75"
            endColumn="43"
            endOffset="2621"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;@color/text_primary&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;@color/text_primary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_primary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/mini_player.xml"
            line="90"
            column="17"
            startOffset="3471"
            endLine="90"
            endColumn="51"
            endOffset="3505"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;@color/text_primary&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;@color/text_primary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_primary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/mini_player.xml"
            line="100"
            column="17"
            startOffset="3942"
            endLine="100"
            endColumn="51"
            endOffset="3976"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Set tint=&quot;@color/text_primary&quot; and Delete tint">
            <fix-attribute
                description="Set tint=&quot;@color/text_primary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_primary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/mini_player.xml"
            line="109"
            column="17"
            startOffset="4357"
            endLine="109"
            endColumn="51"
            endOffset="4391"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/nauh/musicplayer/ui/adapter/SongAdapter.kt"
            line="55"
            column="9"
            startOffset="1789"
            endLine="55"
            endColumn="31"
            endOffset="1811"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Theme.MusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="46"
            endOffset="382"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?android:attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/Theme.MusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_song.xml"
            line="9"
            column="5"
            startOffset="368"
            endLine="9"
            endColumn="64"
            endOffset="427"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/surface` with a theme that also paints a background (inferred theme is `@style/Theme.MusicPlayer`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/mini_player.xml"
            line="8"
            column="5"
            startOffset="339"
            endLine="8"
            endColumn="40"
            endOffset="374"/>
    </incident>

    <incident
        id="UnusedNamespace"
        severity="warning"
        message="Unused namespace `app`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/mini_player.xml"
            line="3"
            column="5"
            startOffset="116"
            endLine="3"
            endColumn="56"
            endOffset="167"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toUri` instead?">
        <fix-replace
            description="Replace with the toUri extension function"
            family="Replace with the toUri extension function"
            robot="true"
            independent="true"
            oldString="android.net.Uri.parse(song.artworkUrl)"
            replacement="song.artworkUrl.toUri()"
            reformat="value"
            imports="androidx.core.net.toUri"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/nauh/musicplayer/service/MusicService.kt"
                startOffset="1500"
                endOffset="1538"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/nauh/musicplayer/service/MusicService.kt"
            line="40"
            column="32"
            startOffset="1500"
            endLine="40"
            endColumn="70"
            endOffset="1538"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-media3-media3-exoplayer2"
            robot="true">
            <fix-replace
                description="Replace with androidxMedia3ExoplayerVersion = &quot;1.2.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxMedia3ExoplayerVersion = &quot;1.2.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-media3-media3-exoplayer2 = { module = &quot;androidx.media3:media3-exoplayer&quot;, version.ref = &quot;androidxMedia3ExoplayerVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-media3-media3-exoplayer2 = { module = &quot;androidx.media3:media3-exoplayer&quot;, version.ref = &quot;androidxMedia3ExoplayerVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.media3.media3.exoplayer2"
                robot="true"
                replacement="libs.androidx.media3.media3.exoplayer2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1154"
                    endOffset="1194"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="48"
            column="20"
            startOffset="1154"
            endLine="48"
            endColumn="60"
            endOffset="1194"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-media3-media3-ui2"
            robot="true">
            <fix-replace
                description="Replace with androidxMedia3UiVersion = &quot;1.2.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxMedia3UiVersion = &quot;1.2.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-media3-media3-ui2 = { module = &quot;androidx.media3:media3-ui&quot;, version.ref = &quot;androidxMedia3UiVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-media3-media3-ui2 = { module = &quot;androidx.media3:media3-ui&quot;, version.ref = &quot;androidxMedia3UiVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.media3.media3.ui2"
                robot="true"
                replacement="libs.androidx.media3.media3.ui2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1215"
                    endOffset="1248"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="49"
            column="20"
            startOffset="1215"
            endLine="49"
            endColumn="53"
            endOffset="1248"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-media3-media3-session2"
            robot="true">
            <fix-replace
                description="Replace with androidxMedia3SessionVersion = &quot;1.2.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxMedia3SessionVersion = &quot;1.2.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-media3-media3-session2 = { module = &quot;androidx.media3:media3-session&quot;, version.ref = &quot;androidxMedia3SessionVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-media3-media3-session2 = { module = &quot;androidx.media3:media3-session&quot;, version.ref = &quot;androidxMedia3SessionVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.media3.media3.session2"
                robot="true"
                replacement="libs.androidx.media3.media3.session2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1269"
                    endOffset="1307"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="50"
            column="20"
            startOffset="1269"
            endLine="50"
            endColumn="58"
            endOffset="1307"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-media3-media3-common2"
            robot="true">
            <fix-replace
                description="Replace with androidxMedia3CommonVersion = &quot;1.2.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxMedia3CommonVersion = &quot;1.2.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-media3-media3-common2 = { module = &quot;androidx.media3:media3-common&quot;, version.ref = &quot;androidxMedia3CommonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-media3-media3-common2 = { module = &quot;androidx.media3:media3-common&quot;, version.ref = &quot;androidxMedia3CommonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.media3.media3.common2"
                robot="true"
                replacement="libs.androidx.media3.media3.common2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1328"
                    endOffset="1365"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="51"
            column="20"
            startOffset="1328"
            endLine="51"
            endColumn="57"
            endOffset="1365"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-media-media2"
            robot="true">
            <fix-replace
                description="Replace with androidxMediaVersion = &quot;1.6.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxMediaVersion = &quot;1.6.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-media-media2 = { module = &quot;androidx.media:media&quot;, version.ref = &quot;androidxMediaVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-media-media2 = { module = &quot;androidx.media:media&quot;, version.ref = &quot;androidxMediaVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.media.media2"
                robot="true"
                replacement="libs.androidx.media.media2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1426"
                    endOffset="1454"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="54"
            column="20"
            startOffset="1426"
            endLine="54"
            endColumn="48"
            endOffset="1454"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-google-guava-guava2"
            robot="true">
            <fix-replace
                description="Replace with googleGuavaVersion = &quot;31.1-android&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="googleGuavaVersion = &quot;31.1-android&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-google-guava-guava2 = { module = &quot;com.google.guava:guava&quot;, version.ref = &quot;googleGuavaVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-google-guava-guava2 = { module = &quot;com.google.guava:guava&quot;, version.ref = &quot;googleGuavaVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.google.guava.guava2"
                robot="true"
                replacement="libs.com.google.guava.guava2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1510"
                    endOffset="1547"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="57"
            column="20"
            startOffset="1510"
            endLine="57"
            endColumn="57"
            endOffset="1547"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-squareup-retrofit2-retrofit"
            robot="true">
            <fix-replace
                description="Replace with squareupRetrofitVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="squareupRetrofitVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="213"
                    endOffset="213"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-squareup-retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;squareupRetrofitVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-squareup-retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;squareupRetrofitVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.squareup.retrofit2.retrofit"
                robot="true"
                replacement="libs.com.squareup.retrofit2.retrofit"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1587"
                    endOffset="1626"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="60"
            column="20"
            startOffset="1587"
            endLine="60"
            endColumn="59"
            endOffset="1626"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-squareup-retrofit2-converter-gson"
            robot="true">
            <fix-replace
                description="Replace with squareupConverterGsonVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="squareupConverterGsonVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="213"
                    endOffset="213"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-squareup-retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;squareupConverterGsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-squareup-retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;squareupConverterGsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.squareup.retrofit2.converter.gson"
                robot="true"
                replacement="libs.com.squareup.retrofit2.converter.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1647"
                    endOffset="1692"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="61"
            column="20"
            startOffset="1647"
            endLine="61"
            endColumn="65"
            endOffset="1692"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-squareup-okhttp3-logging-interceptor"
            robot="true">
            <fix-replace
                description="Replace with squareupLoggingInterceptorVersion = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="squareupLoggingInterceptorVersion = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="213"
                    endOffset="213"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-squareup-okhttp3-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;squareupLoggingInterceptorVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-squareup-okhttp3-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;squareupLoggingInterceptorVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.squareup.okhttp3.logging.interceptor"
                robot="true"
                replacement="libs.com.squareup.okhttp3.logging.interceptor"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1713"
                    endOffset="1762"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="62"
            column="20"
            startOffset="1713"
            endLine="62"
            endColumn="69"
            endOffset="1762"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for com-github-bumptech-glide-glide2"
            robot="true">
            <fix-replace
                description="Replace with githubGlideVersion = &quot;4.16.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="githubGlideVersion = &quot;4.16.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with com-github-bumptech-glide-glide2 = { module = &quot;com.github.bumptech.glide:glide&quot;, version.ref = &quot;githubGlideVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="com-github-bumptech-glide-glide2 = { module = &quot;com.github.bumptech.glide:glide&quot;, version.ref = &quot;githubGlideVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.com.github.bumptech.glide.glide2"
                robot="true"
                replacement="libs.com.github.bumptech.glide.glide2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1805"
                    endOffset="1845"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="65"
            column="20"
            startOffset="1805"
            endLine="65"
            endColumn="60"
            endOffset="1845"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for de-hdodenhof-circleimageview"
            robot="true">
            <fix-replace
                description="Replace with deCircleimageviewVersion = &quot;3.1.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="deCircleimageviewVersion = &quot;3.1.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with de-hdodenhof-circleimageview = { module = &quot;de.hdodenhof:circleimageview&quot;, version.ref = &quot;deCircleimageviewVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="de-hdodenhof-circleimageview = { module = &quot;de.hdodenhof:circleimageview&quot;, version.ref = &quot;deCircleimageviewVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.de.hdodenhof.circleimageview"
                robot="true"
                replacement="libs.de.hdodenhof.circleimageview"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1893"
                    endOffset="1929"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="68"
            column="20"
            startOffset="1893"
            endLine="68"
            endColumn="56"
            endOffset="1929"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-recyclerview-recyclerview2"
            robot="true">
            <fix-replace
                description="Replace with androidxRecyclerviewVersion = &quot;1.3.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxRecyclerviewVersion = &quot;1.3.2&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-recyclerview-recyclerview2 = { module = &quot;androidx.recyclerview:recyclerview&quot;, version.ref = &quot;androidxRecyclerviewVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-recyclerview-recyclerview2 = { module = &quot;androidx.recyclerview:recyclerview&quot;, version.ref = &quot;androidxRecyclerviewVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.recyclerview.recyclerview2"
                robot="true"
                replacement="libs.androidx.recyclerview.recyclerview2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="1971"
                    endOffset="2013"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="71"
            column="20"
            startOffset="1971"
            endLine="71"
            endColumn="62"
            endOffset="2013"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for swiperefreshlayout"
            robot="true">
            <fix-replace
                description="Replace with swiperefreshlayoutVersion = &quot;1.1.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="swiperefreshlayoutVersion = &quot;1.1.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="213"
                    endOffset="213"/>
            </fix-replace>
            <fix-replace
                description="Replace with swiperefreshlayout = { module = &quot;androidx.swiperefreshlayout:swiperefreshlayout&quot;, version.ref = &quot;swiperefreshlayoutVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="swiperefreshlayout = { module = &quot;androidx.swiperefreshlayout:swiperefreshlayout&quot;, version.ref = &quot;swiperefreshlayoutVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1021"
                    endOffset="1021"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.swiperefreshlayout"
                robot="true"
                replacement="libs.swiperefreshlayout"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2061"
                    endOffset="2115"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="74"
            column="20"
            startOffset="2061"
            endLine="74"
            endColumn="74"
            endOffset="2115"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-lifecycle-lifecycle-viewmodel-ktx2"
            robot="true">
            <fix-replace
                description="Replace with androidxLifecycleViewmodelKtxVersion = &quot;2.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxLifecycleViewmodelKtxVersion = &quot;2.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-lifecycle-lifecycle-viewmodel-ktx2 = { module = &quot;androidx.lifecycle:lifecycle-viewmodel-ktx&quot;, version.ref = &quot;androidxLifecycleViewmodelKtxVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-lifecycle-lifecycle-viewmodel-ktx2 = { module = &quot;androidx.lifecycle:lifecycle-viewmodel-ktx&quot;, version.ref = &quot;androidxLifecycleViewmodelKtxVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.lifecycle.lifecycle.viewmodel.ktx2"
                robot="true"
                replacement="libs.androidx.lifecycle.lifecycle.viewmodel.ktx2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2165"
                    endOffset="2215"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="77"
            column="20"
            startOffset="2165"
            endLine="77"
            endColumn="70"
            endOffset="2215"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-lifecycle-lifecycle-livedata-ktx2"
            robot="true">
            <fix-replace
                description="Replace with androidxLifecycleLivedataKtxVersion = &quot;2.7.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxLifecycleLivedataKtxVersion = &quot;2.7.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-lifecycle-lifecycle-livedata-ktx2 = { module = &quot;androidx.lifecycle:lifecycle-livedata-ktx&quot;, version.ref = &quot;androidxLifecycleLivedataKtxVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-lifecycle-lifecycle-livedata-ktx2 = { module = &quot;androidx.lifecycle:lifecycle-livedata-ktx&quot;, version.ref = &quot;androidxLifecycleLivedataKtxVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="318"
                    endOffset="318"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.lifecycle.lifecycle.livedata.ktx2"
                robot="true"
                replacement="libs.androidx.lifecycle.lifecycle.livedata.ktx2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2236"
                    endOffset="2285"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="78"
            column="20"
            startOffset="2236"
            endLine="78"
            endColumn="69"
            endOffset="2285"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for org-jetbrains-kotlinx-kotlinx-coroutines-android2"
            robot="true">
            <fix-replace
                description="Replace with jetbrainsKotlinxCoroutinesAndroidVersion = &quot;1.7.3&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="jetbrainsKotlinxCoroutinesAndroidVersion = &quot;1.7.3&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with org-jetbrains-kotlinx-kotlinx-coroutines-android2 = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-android&quot;, version.ref = &quot;jetbrainsKotlinxCoroutinesAndroidVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="org-jetbrains-kotlinx-kotlinx-coroutines-android2 = { module = &quot;org.jetbrains.kotlinx:kotlinx-coroutines-android&quot;, version.ref = &quot;jetbrainsKotlinxCoroutinesAndroidVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1021"
                    endOffset="1021"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.org.jetbrains.kotlinx.kotlinx.coroutines.android2"
                robot="true"
                replacement="libs.org.jetbrains.kotlinx.kotlinx.coroutines.android2"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2325"
                    endOffset="2381"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="81"
            column="20"
            startOffset="2325"
            endLine="81"
            endColumn="76"
            endOffset="2381"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="36"
            column="10"
            startOffset="1439"
            endLine="36"
            endColumn="19"
            endOffset="1448"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_song.xml"
            line="15"
            column="6"
            startOffset="558"
            endLine="15"
            endColumn="15"
            endOffset="567"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_song.xml"
            line="68"
            column="6"
            startOffset="2332"
            endLine="68"
            endColumn="15"
            endOffset="2341"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/mini_player.xml"
            line="33"
            column="10"
            startOffset="1237"
            endLine="33"
            endColumn="19"
            endOffset="1246"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;No songs found&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="80"
            column="13"
            startOffset="3395"
            endLine="80"
            endColumn="42"
            endOffset="3424"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Back&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="18"
            column="9"
            startOffset="742"
            endLine="18"
            endColumn="42"
            endOffset="775"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Shuffle&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="156"
            column="13"
            startOffset="5850"
            endLine="156"
            endColumn="49"
            endOffset="5886"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Previous&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="165"
            column="13"
            startOffset="6233"
            endLine="165"
            endColumn="50"
            endOffset="6270"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Play&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="180"
            column="17"
            startOffset="6826"
            endLine="180"
            endColumn="50"
            endOffset="6859"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Next&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="191"
            column="13"
            startOffset="7244"
            endLine="191"
            endColumn="46"
            endOffset="7277"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Repeat&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_player.xml"
            line="199"
            column="13"
            startOffset="7564"
            endLine="199"
            endColumn="48"
            endOffset="7599"/>
    </incident>

</incidents>
