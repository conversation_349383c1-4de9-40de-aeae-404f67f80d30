<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="partial_results">
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.nauh.musicplayer.ui.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.accent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="32"
            column="12"
            startOffset="1142"
            endLine="32"
            endColumn="25"
            endOffset="1155"/>
        <location id="R.color.background_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="19"
            column="12"
            startOffset="681"
            endLine="19"
            endColumn="39"
            endOffset="708"/>
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="62"
            endLine="3"
            endColumn="24"
            endOffset="74"/>
        <location id="R.color.error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="33"
            column="12"
            startOffset="1183"
            endLine="33"
            endColumn="24"
            endOffset="1195"/>
        <location id="R.color.headphone_pink"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="14"
            column="12"
            startOffset="468"
            endLine="14"
            endColumn="33"
            endOffset="489"/>
        <location id="R.color.headphone_red"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="15"
            column="12"
            startOffset="517"
            endLine="15"
            endColumn="32"
            endOffset="537"/>
        <location id="R.color.primary_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="299"
            endLine="9"
            endColumn="32"
            endOffset="319"/>
        <location id="R.color.progress_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="29"
            column="12"
            startOffset="1060"
            endLine="29"
            endColumn="38"
            endOffset="1086"/>
        <location id="R.color.success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="34"
            column="12"
            startOffset="1223"
            endLine="34"
            endColumn="26"
            endOffset="1237"/>
        <location id="R.color.surface_variant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="21"
            column="12"
            startOffset="778"
            endLine="21"
            endColumn="34"
            endOffset="800"/>
        <location id="R.color.text_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="25"
            column="12"
            startOffset="933"
            endLine="25"
            endColumn="33"
            endOffset="954"/>
        <location id="R.color.text_tertiary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="26"
            column="12"
            startOffset="982"
            endLine="26"
            endColumn="32"
            endOffset="1002"/>
        <location id="R.color.warning"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="35"
            column="12"
            startOffset="1265"
            endLine="35"
            endColumn="26"
            endOffset="1279"/>
        <location id="R.drawable.ic_equalizer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_equalizer.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="371"/>
        <location id="R.drawable.ic_more_vert"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_more_vert.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="498"/>
        <location id="R.drawable.ic_play_arrow"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play_arrow.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="328"/>
        <location id="R.drawable.ic_search"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="562"/>
        <location id="R.layout.mini_player"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/mini_player.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="115"
            endColumn="16"
            endOffset="4457"/>
        <location id="R.string.album_artwork"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="29"
            column="13"
            startOffset="1171"
            endLine="29"
            endColumn="33"
            endOffset="1191"/>
        <location id="R.string.error_loading_songs"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="24"
            column="13"
            startOffset="913"
            endLine="24"
            endColumn="39"
            endOffset="939"/>
        <location id="R.string.error_network"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="25"
            column="13"
            startOffset="982"
            endLine="25"
            endColumn="33"
            endOffset="1002"/>
        <location id="R.string.error_playback"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1069"
            endLine="26"
            endColumn="34"
            endOffset="1090"/>
        <location id="R.string.more_options"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="17"
            column="13"
            startOffset="644"
            endLine="17"
            endColumn="32"
            endOffset="663"/>
        <location id="R.string.next"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="13"
            startOffset="520"
            endLine="14"
            endColumn="24"
            endOffset="531"/>
        <location id="R.string.no_songs_found"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="173"
            endLine="6"
            endColumn="34"
            endOffset="194"/>
        <location id="R.string.notification_channel_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="795"
            endLine="21"
            endColumn="52"
            endOffset="834"/>
        <location id="R.string.notification_channel_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="726"
            endLine="20"
            endColumn="45"
            endOffset="758"/>
        <location id="R.string.now_playing"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="344"
            endLine="10"
            endColumn="31"
            endOffset="362"/>
        <location id="R.string.pause"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="12"
            column="13"
            startOffset="434"
            endLine="12"
            endColumn="25"
            endOffset="446"/>
        <location id="R.string.play"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="11"
            column="13"
            startOffset="396"
            endLine="11"
            endColumn="24"
            endOffset="407"/>
        <location id="R.string.playing_indicator"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="1227"
            endLine="30"
            endColumn="37"
            endOffset="1251"/>
        <location id="R.string.previous"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="13"
            column="13"
            startOffset="474"
            endLine="13"
            endColumn="28"
            endOffset="489"/>
        <location id="R.string.repeat"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="602"
            endLine="16"
            endColumn="26"
            endOffset="615"/>
        <location id="R.string.search_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="102"
            endLine="5"
            endColumn="31"
            endOffset="120"/>
        <location id="R.string.shuffle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="558"
            endLine="15"
            endColumn="27"
            endOffset="572"/>
        <location id="R.string.try_refreshing"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="231"
            endLine="7"
            endColumn="34"
            endOffset="252"/>
        <entry
            name="model"
            string="attr[colorOnSurface(R),colorOnPrimary(R),actionBarSize(R)],color[colorPrimary(U),surface(U),progress_background(D),primary(U),text_primary(U),text_secondary(D),black(D),white(U),primary_dark(U),primary_light(D),colorAccent(U),headphone_pink(D),headphone_red(D),background_primary(U),background_secondary(D),surface_variant(D),text_tertiary(D),accent(D),error(D),success(D),warning(D)],drawable[ic_arrow_back(U),ic_equalizer(D),ic_launcher_background(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R),ic_more_vert(D),ic_music_note(U),ic_pause(U),ic_play(U),ic_play_arrow(D),ic_repeat(U),ic_repeat_one(U),ic_search(D),ic_shuffle(U),ic_skip_next(U),ic_skip_previous(U),placeholder_album_art(U)],id[swipeRefreshLayout(U),main(D),toolbar(U),searchCard(U),searchView(U),recyclerViewSongs(U),progressBar(U),tvEmptyState(U),btnBack(U),albumArtworkCard(U),ivAlbumArt(U),songInfoLayout(U),tvSongTitle(U),tvArtistName(U),progressLayout(U),seekBar(U),tvCurrentTime(U),tvTotalTime(U),controlButtonsLayout(D),btnShuffle(U),btnPrevious(U),btnPlayPause(U),btnNext(U),btnRepeat(U),tvTitle(U),tvArtist(U),tvDuration(U),ivPlayingIndicator(U),miniProgressBar(D),miniAlbumArtwork(D),miniSongTitle(D),miniArtistName(D),miniPreviousButton(D),miniPlayPauseButton(D),miniNextButton(D)],layout[activity_main(U),item_song(U),activity_player(U),mini_player(D)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U),previous(D),pause(D),next(D),search_hint(D),no_songs_found(D),try_refreshing(D),now_playing(D),play(D),shuffle(D),repeat(D),more_options(D),notification_channel_name(D),notification_channel_description(D),error_loading_songs(D),error_network(D),error_playback(D),album_artwork(D),playing_indicator(D)],style[Theme_MusicPlayer(U),Base_Theme_MusicPlayer(U),Theme_Material3_Dark_NoActionBar(R),Theme_Material3_DayNight_NoActionBar(R)],xml[data_extraction_rules(U),backup_rules(U)];18^0,19^0,1b^1c,1d^0,1e^0,1f^0,20^1,21^0,22^0,23^0,24^0,25^0,26^0,27^0,4c^3^2^2b^2c^4d,4d^28^1e^3,4e^18^31^28^32^34^3^37^25^27^20^26^22,4f^4^5^6^28^7^8^53^27^54^1f^55^26,50^1a^1b,51^1a^1b,65^66,66^67^6^b^a^10^4^7^68;;;"/>
    </map>

</incidents>
