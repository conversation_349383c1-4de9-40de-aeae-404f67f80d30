{"logs": [{"outputFile": "com.nauh.musicplayer.app-mergeReleaseResources-40:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8c6e034149c65399529a9cd0f184f8b\\transformed\\media3-session-1.2.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,118,199,267,334,412,493,578", "endColumns": "62,80,67,66,77,80,84,90", "endOffsets": "113,194,262,329,407,488,573,664"}, "to": {"startLines": "53,69,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3719,5185,10056,10124,10191,10269,10350,10435", "endColumns": "62,80,67,66,77,80,84,90", "endOffsets": "3777,5261,10119,10186,10264,10345,10430,10521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e355fbe8fe59cc5407000c654ab4b66\\transformed\\appcompat-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1063,1168,1268,1376,1460,1562,1678,1757,1835,1926,2020,2114,2208,2308,2401,2496,2589,2680,2772,2853,2958,3061,3159,3264,3366,3468,3622,13896", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "1163,1263,1371,1455,1557,1673,1752,1830,1921,2015,2109,2203,2303,2396,2491,2584,2675,2767,2848,2953,3056,3154,3259,3361,3463,3617,3714,13973"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c077dfbc8dc20562e970eb684d92a281\\transformed\\media3-ui-1.2.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,523,757,836,914,990,1075,1159,1221,1283,1372,1458,1523,1587,1650,1718,1838,1948,2066,2137,2214,2283,2344,2434,2523,2587,2650,2704,2775,2823,2884,2943,3010,3071,3134,3195,3252,3318,3382,3448,3500,3554,3622,3690", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,119,109,117,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "279,518,752,831,909,985,1070,1154,1216,1278,1367,1453,1518,1582,1645,1713,1833,1943,2061,2132,2209,2278,2339,2429,2518,2582,2645,2699,2770,2818,2879,2938,3005,3066,3129,3190,3247,3313,3377,3443,3495,3549,3617,3685,3739"}, "to": {"startLines": "2,11,16,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,379,618,5331,5410,5488,5564,5649,5733,5795,5857,5946,6032,6097,6161,6224,6292,6412,6522,6640,6711,6788,6857,6918,7008,7097,7161,7838,7892,7963,8011,8072,8131,8198,8259,8322,8383,8440,8506,8570,8636,8688,8742,8810,8878", "endLines": "10,15,20,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,119,109,117,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "374,613,847,5405,5483,5559,5644,5728,5790,5852,5941,6027,6092,6156,6219,6287,6407,6517,6635,6706,6783,6852,6913,7003,7092,7156,7219,7887,7958,8006,8067,8126,8193,8254,8317,8378,8435,8501,8565,8631,8683,8737,8805,8873,8927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d41d36b14e47c257e85459df73d97ebd\\transformed\\media3-exoplayer-1.2.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,311,385,447,527,607", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "115,174,241,306,380,442,522,602,664"}, "to": {"startLines": "95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7224,7289,7348,7415,7480,7554,7616,7696,7776", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "7284,7343,7410,7475,7549,7611,7691,7771,7833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c6ed593d095e40b444f65ff3ae72d38a\\transformed\\material-1.10.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,640,731,824,937,1017,1082,1170,1240,1303,1395,1458,1518,1577,1640,1701,1755,1857,1914,1973,2027,2095,2206,2287,2369,2501,2572,2645,2769,2857,2933,2986,3040,3106,3179,3255,3341,3419,3489,3564,3646,3714,3815,3900,3970,4060,4151,4225,4298,4387,4438,4519,4586,4668,4753,4815,4879,4942,5010,5104,5199,5289,5386,5443,5501", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,76,74,76,99,90,92,112,79,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,123,87,75,52,53,65,72,75,85,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74", "endOffsets": "306,383,458,535,635,726,819,932,1012,1077,1165,1235,1298,1390,1453,1513,1572,1635,1696,1750,1852,1909,1968,2022,2090,2201,2282,2364,2496,2567,2640,2764,2852,2928,2981,3035,3101,3174,3250,3336,3414,3484,3559,3641,3709,3810,3895,3965,4055,4146,4220,4293,4382,4433,4514,4581,4663,4748,4810,4874,4937,5005,5099,5194,5284,5381,5438,5496,5571"}, "to": {"startLines": "21,54,55,56,57,58,66,67,68,70,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "852,3782,3859,3934,4011,4111,4899,4992,5105,5266,8932,9020,9090,9153,9245,9308,9368,9427,9490,9551,9605,9707,9764,9823,9877,9945,10526,10607,10689,10821,10892,10965,11089,11177,11253,11306,11360,11426,11499,11575,11661,11739,11809,11884,11966,12034,12135,12220,12290,12380,12471,12545,12618,12707,12758,12839,12906,12988,13073,13135,13199,13262,13330,13424,13519,13609,13706,13763,13821", "endLines": "25,54,55,56,57,58,66,67,68,70,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186", "endColumns": "12,76,74,76,99,90,92,112,79,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,123,87,75,52,53,65,72,75,85,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74", "endOffsets": "1058,3854,3929,4006,4106,4197,4987,5100,5180,5326,9015,9085,9148,9240,9303,9363,9422,9485,9546,9600,9702,9759,9818,9872,9940,10051,10602,10684,10816,10887,10960,11084,11172,11248,11301,11355,11421,11494,11570,11656,11734,11804,11879,11961,12029,12130,12215,12285,12375,12466,12540,12613,12702,12753,12834,12901,12983,13068,13130,13194,13257,13325,13419,13514,13604,13701,13758,13816,13891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87c47c7abe7f29a54b096c7ff4d96851\\transformed\\core-1.16.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "59,60,61,62,63,64,65,188", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4202,4296,4398,4495,4592,4693,4793,13978", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "4291,4393,4490,4587,4688,4788,4894,14074"}}]}]}