{"logs": [{"outputFile": "com.nauh.musicplayer.app-mergeReleaseResources-40:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e355fbe8fe59cc5407000c654ab4b66\\transformed\\appcompat-1.6.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1334,1454,1557,1673,1759,1864,1983,2063,2140,2232,2326,2421,2515,2610,2704,2800,2895,2987,3079,3160,3266,3371,3469,3577,3683,3791,3964,14847", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "1449,1552,1668,1754,1859,1978,2058,2135,2227,2321,2416,2510,2605,2699,2795,2890,2982,3074,3155,3261,3366,3464,3572,3678,3786,3959,4059,14924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8c6e034149c65399529a9cd0f184f8b\\transformed\\media3-session-1.2.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,134,208,280,351,432,517,618", "endColumns": "78,73,71,70,80,84,100,105", "endOffsets": "129,203,275,346,427,512,613,719"}, "to": {"startLines": "56,72,141,142,143,144,145,146", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4064,5611,10750,10822,10893,10974,11059,11160", "endColumns": "78,73,71,70,80,84,100,105", "endOffsets": "4138,5680,10817,10888,10969,11054,11155,11261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87c47c7abe7f29a54b096c7ff4d96851\\transformed\\core-1.16.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "62,63,64,65,66,67,68,191", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4568,4666,4768,4868,4969,5075,5178,14929", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "4661,4763,4863,4964,5070,5173,5294,15025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d41d36b14e47c257e85459df73d97ebd\\transformed\\media3-exoplayer-1.2.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,257,320,397,465,564,660", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "121,185,252,315,392,460,559,655,725"}, "to": {"startLines": "98,99,100,101,102,103,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7721,7792,7856,7923,7986,8063,8131,8230,8326", "endColumns": "70,63,66,62,76,67,98,95,69", "endOffsets": "7787,7851,7918,7981,8058,8126,8225,8321,8391"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c077dfbc8dc20562e970eb684d92a281\\transformed\\media3-ui-1.2.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,627,957,1040,1123,1206,1304,1402,1491,1555,1648,1742,1807,1872,1937,2005,2100,2194,2294,2371,2450,2519,2609,2702,2795,2861,2926,2979,3039,3087,3148,3221,3289,3354,3427,3492,3550,3616,3681,3747,3799,3859,3933,4007", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,94,93,99,76,78,68,89,92,92,65,64,52,59,47,60,72,67,64,72,64,57,65,64,65,51,59,73,73,54", "endOffsets": "283,622,952,1035,1118,1201,1299,1397,1486,1550,1643,1737,1802,1867,1932,2000,2095,2189,2289,2366,2445,2514,2604,2697,2790,2856,2921,2974,3034,3082,3143,3216,3284,3349,3422,3487,3545,3611,3676,3742,3794,3854,3928,4002,4057"}, "to": {"startLines": "2,11,17,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,383,722,5752,5835,5918,6001,6099,6197,6286,6350,6443,6537,6602,6667,6732,6800,6895,6989,7089,7166,7245,7314,7404,7497,7590,7656,8396,8449,8509,8557,8618,8691,8759,8824,8897,8962,9020,9086,9151,9217,9269,9329,9403,9477", "endLines": "10,16,22,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "endColumns": "17,12,12,82,82,82,97,97,88,63,92,93,64,64,64,67,94,93,99,76,78,68,89,92,92,65,64,52,59,47,60,72,67,64,72,64,57,65,64,65,51,59,73,73,54", "endOffsets": "378,717,1047,5830,5913,5996,6094,6192,6281,6345,6438,6532,6597,6662,6727,6795,6890,6984,7084,7161,7240,7309,7399,7492,7585,7651,7716,8444,8504,8552,8613,8686,8754,8819,8892,8957,9015,9081,9146,9212,9264,9324,9398,9472,9527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c6ed593d095e40b444f65ff3ae72d38a\\transformed\\material-1.10.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1186,1279,1356,1419,1535,1598,1667,1726,1797,1856,1910,2031,2092,2155,2209,2282,2404,2492,2575,2727,2813,2900,3033,3124,3207,3264,3315,3381,3453,3530,3614,3697,3772,3849,3931,4007,4115,4204,4286,4377,4473,4547,4628,4723,4777,4859,4925,5012,5098,5160,5224,5287,5356,5466,5579,5682,5789,5850,5905", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,132,90,82,56,50,65,71,76,83,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1181,1274,1351,1414,1530,1593,1662,1721,1792,1851,1905,2026,2087,2150,2204,2277,2399,2487,2570,2722,2808,2895,3028,3119,3202,3259,3310,3376,3448,3525,3609,3692,3767,3844,3926,4002,4110,4199,4281,4372,4468,4542,4623,4718,4772,4854,4920,5007,5093,5155,5219,5282,5351,5461,5574,5677,5784,5845,5900,5980"}, "to": {"startLines": "23,57,58,59,60,61,69,70,71,73,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1052,4143,4220,4297,4379,4476,5299,5396,5528,5685,9532,9625,9702,9765,9881,9944,10013,10072,10143,10202,10256,10377,10438,10501,10555,10628,11266,11354,11437,11589,11675,11762,11895,11986,12069,12126,12177,12243,12315,12392,12476,12559,12634,12711,12793,12869,12977,13066,13148,13239,13335,13409,13490,13585,13639,13721,13787,13874,13960,14022,14086,14149,14218,14328,14441,14544,14651,14712,14767", "endLines": "28,57,58,59,60,61,69,70,71,73,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,132,90,82,56,50,65,71,76,83,82,74,76,81,75,107,88,81,90,95,73,80,94,53,81,65,86,85,61,63,62,68,109,112,102,106,60,54,79", "endOffsets": "1329,4215,4292,4374,4471,4563,5391,5523,5606,5747,9620,9697,9760,9876,9939,10008,10067,10138,10197,10251,10372,10433,10496,10550,10623,10745,11349,11432,11584,11670,11757,11890,11981,12064,12121,12172,12238,12310,12387,12471,12554,12629,12706,12788,12864,12972,13061,13143,13234,13330,13404,13485,13580,13634,13716,13782,13869,13955,14017,14081,14144,14213,14323,14436,14539,14646,14707,14762,14842"}}]}]}