<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface"
        android:padding="12dp">

        <!-- Album Artwork -->
        <ImageView
            android:id="@+id/miniAlbumArtwork"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:scaleType="centerCrop"
            android:background="@drawable/placeholder_album_art"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:src="@drawable/placeholder_album_art" />

        <!-- Song Info -->
        <LinearLayout
            android:id="@+id/songInfoLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/miniAlbumArtwork"
            app:layout_constraintEnd_toStartOf="@id/controlsLayout"
            app:layout_constraintStart_toEndOf="@id/miniAlbumArtwork"
            app:layout_constraintTop_toTopOf="@id/miniAlbumArtwork">

            <TextView
                android:id="@+id/miniSongTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="Song Title" />

            <TextView
                android:id="@+id/miniArtistName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="Artist Name" />

        </LinearLayout>

        <!-- Control Buttons -->
        <LinearLayout
            android:id="@+id/controlsLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@id/miniAlbumArtwork"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/miniAlbumArtwork">

            <ImageButton
                android:id="@+id/miniPreviousButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_skip_previous"
                android:contentDescription="@string/previous"
                app:tint="@color/text_primary" />

            <ImageButton
                android:id="@+id/miniPlayPauseButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginHorizontal="8dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_play_arrow"
                android:contentDescription="@string/play"
                app:tint="@color/primary" />

            <ImageButton
                android:id="@+id/miniNextButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_skip_next"
                android:contentDescription="@string/next"
                app:tint="@color/text_primary" />

        </LinearLayout>

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/miniProgressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="0dp"
            android:layout_height="2dp"
            android:layout_marginTop="8dp"
            android:progressTint="@color/primary"
            android:progressBackgroundTint="@color/progress_background"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/miniAlbumArtwork"
            tools:progress="45" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
