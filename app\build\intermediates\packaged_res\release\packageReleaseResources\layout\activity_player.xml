<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".ui.PlayerActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/playerToolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="Now Playing"
        app:titleTextColor="@color/white" />

    <!-- Album Artwork -->
    <androidx.cardview.widget.CardView
        android:id="@+id/albumArtworkCard"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="32dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/playerToolbar"
        app:layout_constraintWidth_max="320dp">

        <ImageView
            android:id="@+id/playerAlbumArtwork"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:background="@drawable/placeholder_album_art"
            tools:src="@drawable/placeholder_album_art" />

    </androidx.cardview.widget.CardView>

    <!-- Song Information -->
    <LinearLayout
        android:id="@+id/songInfoLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/albumArtworkCard">

        <TextView
            android:id="@+id/playerSongTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="2"
            android:textColor="@color/text_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            tools:text="Song Title" />

        <TextView
            android:id="@+id/playerArtistName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/text_secondary"
            android:textSize="18sp"
            tools:text="Artist Name" />

        <TextView
            android:id="@+id/playerAlbumName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/text_secondary"
            android:textSize="16sp"
            tools:text="Album Name" />

    </LinearLayout>

    <!-- Progress Section -->
    <LinearLayout
        android:id="@+id/progressLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="32dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/songInfoLayout">

        <SeekBar
            android:id="@+id/seekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:progressTint="@color/primary"
            android:thumbTint="@color/primary"
            android:progressBackgroundTint="@color/progress_background"
            tools:progress="45" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/currentTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="1:23" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/totalTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="3:45" />

        </LinearLayout>

    </LinearLayout>

    <!-- Control Buttons -->
    <LinearLayout
        android:id="@+id/controlButtonsLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/progressLayout">

        <ImageButton
            android:id="@+id/shuffleButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_shuffle"
            android:contentDescription="@string/shuffle"
            app:tint="@color/text_secondary" />

        <ImageButton
            android:id="@+id/previousButton"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginHorizontal="16dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_skip_previous"
            android:contentDescription="@string/previous"
            app:tint="@color/text_primary" />

        <androidx.cardview.widget.CardView
            android:layout_width="72dp"
            android:layout_height="72dp"
            app:cardCornerRadius="36dp"
            app:cardElevation="4dp"
            android:layout_marginHorizontal="16dp">

            <ImageButton
                android:id="@+id/playPauseButton"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/primary"
                android:src="@drawable/ic_play_arrow"
                android:contentDescription="@string/play"
                app:tint="@color/white" />

        </androidx.cardview.widget.CardView>

        <ImageButton
            android:id="@+id/nextButton"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginHorizontal="16dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_skip_next"
            android:contentDescription="@string/next"
            app:tint="@color/text_primary" />

        <ImageButton
            android:id="@+id/repeatButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_repeat"
            android:contentDescription="@string/repeat"
            app:tint="@color/text_secondary" />

    </LinearLayout>

    <!-- Loading Progress Bar -->
    <ProgressBar
        android:id="@+id/playerProgressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
