# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\222\375\017\002\273YO\a\264\017\202p\3533**\341\345\030\313\246\376\307\320b\363\366\004<\231\315\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.3.6"
  }
  digests {
    sha256: "\022\360\203\033O\b\t-]\332\',\031#\301\032\002/\362\f\357\376\323\350\001u\036!\273\215\034\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.10.0"
  }
  digests {
    sha256: "- N\344\361\026\271\210\246\246\265\333\214\364=\343\370\247M\n\341\272\027Xo\220\252\343\216>\032-"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.2"
  }
  digests {
    sha256: "\000\\\365\025\020I:$\372H\272\256\032EZRXQS\2215\032qq}\323<\272\225!\031f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.1.0-beta02"
  }
  digests {
    sha256: "\272\372\303\312\231\036\326\212,|\246\3775)f\322\000\261.f\243B\321\017I|\270\026\217Y\005J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer"
    version: "1.2.1"
  }
  digests {
    sha256: "\205 L/\274\264\352h\375\021\271\304]\241ki\030\230\024z!\343\351\331W\226\255[E\304\0370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-common"
    version: "1.2.1"
  }
  digests {
    sha256: "=\3353\350\301\024\266\022v\255\304\352\260\342\310xT\t?\306x\350\273\2140\276;\251o\323\267\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-container"
    version: "1.2.1"
  }
  digests {
    sha256: "\346}\213P\375\261t\252##\217y\263X:H\\\254\251e\205$\265\006\265\352\326\351\304}\033d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-database"
    version: "1.2.1"
  }
  digests {
    sha256: "%M`\b\031\352\003\354L!\350<:\277\357\322,\035\030d\aN\\\252\247S\030\217lE\021\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-datasource"
    version: "1.2.1"
  }
  digests {
    sha256: "m\240\366\210\\\205:\037\371\342\207O\216\317hM{\215\030\324]\214\030\274\033|I_\005\245\264\325"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-decoder"
    version: "1.2.1"
  }
  digests {
    sha256: "\216D\212\203\257\364|~\255\264\333\333\374\344\200\206\255\315\370P\276\3103E\n\346\222\006\370>\'m"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-extractor"
    version: "1.2.1"
  }
  digests {
    sha256: "f\225[\177[\315\t\323I\231r;w\314\307\'\253\315\271o\332\221M\302\346c\302\202q\217\356\355"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-session"
    version: "1.2.1"
  }
  digests {
    sha256: "\254\366\257w\024\373j\005Yf\204[\346~<nC(K\242\357w\343BI\313\247\b\v\343\a\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.6.0"
  }
  digests {
    sha256: "\376b\210G\330\371\t\312\217#\326\205\006\037\232v\204\252\f\233\311\301\262\001\304\377n\267\2637(("
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-ui"
    version: "1.2.1"
  }
  digests {
    sha256: "\322\f\f\373)\271\344\022\341\224\037\302m\205c\030\321\257\221(H\343\336\271r\205iP\366a\312\337"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.9.0"
  }
  digests {
    sha256: "2\252 k\232)\311\337^\332\223\240\222\317\263\260\271\023>#,\006+\252\210/\003\031\360\347\237\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.8.5"
  }
  digests {
    sha256: "#:\001I\3746\\\237n\333\326\203\317\342f\261\233\334w;\351\216\253\332\366\263\311$\264\216}\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.16.0"
  }
  digests {
    sha256: "\211\201\034c\335&jHQ\375\033y\306\374\f\230)\226\354\3445\365\377H?\2700\312l\265<\324"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.16.0"
  }
  digests {
    sha256: "\225_\207*\364\322\243!\376\242\243F\374G\"%\242\271\225$\254\304g\201I\027\022\343\233j!O"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.16.0"
  }
  digests {
    sha256: "\242\'\366U\234\020J\245\245\310\213\np\351\307\343\333hY\343\vGt\363\202\221!\337\236bst"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.16.0"
  }
  digests {
    sha256: "\305\2369\261\263\033MU\222\'|\331\000\022\024\235t\vV\235C8\365dC\312\310,?\275\243\321"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "de.hdodenhof"
    artifactId: "circleimageview"
    version: "3.1.0"
  }
  digests {
    sha256: "\216\231e\265@r\356\025\220t\245]\362\026\341}Zb,\224\316\221^\363\021\261\241\363&`\307\373"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.1.0"
  }
  digests {
    sha256: ",\347\220l\321\336\240Z\354\201\227]\262-T8#Y\300Z!\262Rz\330H\274`\366\262r\223"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "2.0.21"
  }
  digests {
    sha256: "\210\226\256o\325\2560\217\360P\025`\317\262\342\234\266\363s\2038\237\234\226e\251\344\334\215\177\373\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "2.0.21"
  }
  digests {
    sha256: "r\030\210h\243\234\202T\022RR\243\300.*\365\274x\035\a\216I\203v\340~\235$\362\217\212\372"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 31
  library_dep_index: 36
  library_dep_index: 0
  library_dep_index: 37
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 0
}
library_dependencies {
  library_index: 12
  library_dep_index: 6
  library_dep_index: 13
}
library_dependencies {
  library_index: 14
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 15
  library_dep_index: 6
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 35
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 28
  library_dep_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 17
  library_dep_index: 6
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
  library_dep_index: 17
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 2
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 1
  library_dep_index: 23
  library_dep_index: 4
  library_dep_index: 2
}
library_dependencies {
  library_index: 23
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 21
}
library_dependencies {
  library_index: 24
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 19
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 25
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 26
  library_dep_index: 25
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 27
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 29
  library_dep_index: 25
}
library_dependencies {
  library_index: 28
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 16
  library_dep_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 29
  library_dep_index: 6
  library_dep_index: 16
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 30
  library_dep_index: 6
  library_dep_index: 31
}
library_dependencies {
  library_index: 31
  library_dep_index: 6
}
library_dependencies {
  library_index: 32
  library_dep_index: 28
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 19
  library_dep_index: 27
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 33
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 29
  library_dep_index: 25
}
library_dependencies {
  library_index: 33
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 25
  library_dep_index: 28
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 20
  library_dep_index: 19
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 32
}
library_dependencies {
  library_index: 34
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 0
}
library_dependencies {
  library_index: 35
  library_dep_index: 6
  library_dep_index: 12
  library_dep_index: 30
  library_dep_index: 13
}
library_dependencies {
  library_index: 36
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 38
  library_dep_index: 39
  library_dep_index: 6
  library_dep_index: 40
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 5
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 48
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 51
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 40
}
library_dependencies {
  library_index: 39
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 19
  library_dep_index: 16
  library_dep_index: 28
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 34
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 0
}
library_dependencies {
  library_index: 40
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 38
}
library_dependencies {
  library_index: 41
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 42
  library_dep_index: 41
  library_dep_index: 15
  library_dep_index: 10
}
library_dependencies {
  library_index: 43
  library_dep_index: 6
}
library_dependencies {
  library_index: 44
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 46
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 47
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 46
}
library_dependencies {
  library_index: 48
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 39
  library_dep_index: 25
  library_dep_index: 28
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 9
}
library_dependencies {
  library_index: 49
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 45
}
library_dependencies {
  library_index: 50
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 24
  library_dep_index: 28
}
library_dependencies {
  library_index: 51
  library_dep_index: 6
}
library_dependencies {
  library_index: 52
  library_dep_index: 53
  library_dep_index: 54
  library_dep_index: 39
  library_dep_index: 6
  library_dep_index: 38
  library_dep_index: 55
  library_dep_index: 56
  library_dep_index: 57
  library_dep_index: 8
  library_dep_index: 44
  library_dep_index: 59
  library_dep_index: 9
  library_dep_index: 48
  library_dep_index: 16
  library_dep_index: 64
  library_dep_index: 51
  library_dep_index: 67
  library_dep_index: 41
  library_dep_index: 66
}
library_dependencies {
  library_index: 53
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 55
  library_dep_index: 6
}
library_dependencies {
  library_index: 56
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 45
  library_dep_index: 10
}
library_dependencies {
  library_index: 57
  library_dep_index: 38
  library_dep_index: 8
  library_dep_index: 58
}
library_dependencies {
  library_index: 59
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 61
  library_dep_index: 50
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 61
  library_dep_index: 6
}
library_dependencies {
  library_index: 62
  library_dep_index: 6
}
library_dependencies {
  library_index: 63
  library_dep_index: 6
}
library_dependencies {
  library_index: 64
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 45
  library_dep_index: 65
  library_dep_index: 66
}
library_dependencies {
  library_index: 65
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 66
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 48
  library_dep_index: 64
}
library_dependencies {
  library_index: 67
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 68
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 76
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 77
}
library_dependencies {
  library_index: 69
  library_dep_index: 6
}
library_dependencies {
  library_index: 70
  library_dep_index: 6
  library_dep_index: 71
  library_dep_index: 9
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 68
  library_dep_index: 80
  library_dep_index: 81
  library_dep_index: 83
}
library_dependencies {
  library_index: 71
  library_dep_index: 72
  library_dep_index: 13
  library_dep_index: 73
  library_dep_index: 74
  library_dep_index: 54
  library_dep_index: 75
}
library_dependencies {
  library_index: 76
  library_dep_index: 70
  library_dep_index: 6
}
library_dependencies {
  library_index: 77
  library_dep_index: 70
  library_dep_index: 6
}
library_dependencies {
  library_index: 78
  library_dep_index: 70
  library_dep_index: 77
  library_dep_index: 6
  library_dep_index: 69
}
library_dependencies {
  library_index: 79
  library_dep_index: 70
  library_dep_index: 6
}
library_dependencies {
  library_index: 80
  library_dep_index: 6
  library_dep_index: 70
  library_dep_index: 76
  library_dep_index: 79
}
library_dependencies {
  library_index: 81
  library_dep_index: 78
  library_dep_index: 10
  library_dep_index: 82
  library_dep_index: 8
  library_dep_index: 70
}
library_dependencies {
  library_index: 82
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
}
library_dependencies {
  library_index: 83
  library_dep_index: 70
  library_dep_index: 82
  library_dep_index: 6
  library_dep_index: 64
}
library_dependencies {
  library_index: 84
  library_dep_index: 85
}
library_dependencies {
  library_index: 85
  library_dep_index: 86
  library_dep_index: 2
}
library_dependencies {
  library_index: 86
  library_dep_index: 87
}
library_dependencies {
  library_index: 87
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 88
  library_dep_index: 84
  library_dep_index: 89
}
library_dependencies {
  library_index: 90
  library_dep_index: 85
  library_dep_index: 2
}
library_dependencies {
  library_index: 91
  library_dep_index: 92
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 48
  library_dep_index: 42
  library_dep_index: 69
  library_dep_index: 31
}
library_dependencies {
  library_index: 92
  library_dep_index: 6
}
library_dependencies {
  library_index: 96
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 15
}
library_dependencies {
  library_index: 97
  library_dep_index: 0
  library_dep_index: 98
}
library_dependencies {
  library_index: 98
  library_dep_index: 0
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 38
  dependency_index: 52
  dependency_index: 39
  dependency_index: 57
  dependency_index: 68
  dependency_index: 83
  dependency_index: 81
  dependency_index: 70
  dependency_index: 82
  dependency_index: 71
  dependency_index: 84
  dependency_index: 88
  dependency_index: 90
  dependency_index: 91
  dependency_index: 95
  dependency_index: 64
  dependency_index: 96
  dependency_index: 32
  dependency_index: 27
  dependency_index: 20
  dependency_index: 97
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
