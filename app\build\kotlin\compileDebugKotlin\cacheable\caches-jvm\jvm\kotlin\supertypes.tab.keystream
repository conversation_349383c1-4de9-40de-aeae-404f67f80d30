+com.nauh.musicplayer.MusicPlayerApplication(com.nauh.musicplayer.data.model.Playlist$com.nauh.musicplayer.data.model.Song,com.nauh.musicplayer.presenter.MainPresenter.com.nauh.musicplayer.presenter.PlayerPresenter)com.nauh.musicplayer.service.MusicService$com.nauh.musicplayer.ui.MainActivity&com.nauh.musicplayer.ui.PlayerActivity+com.nauh.musicplayer.ui.adapter.SongAdapter:com.nauh.musicplayer.ui.adapter.SongAdapter.SongViewHolder<com.nauh.musicplayer.ui.adapter.SongAdapter.SongDiffCallback>com.nauh.musicplayer.service.MusicService.MediaSessionCallback                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 