{"logs": [{"outputFile": "com.nauh.musicplayer.app-mergeReleaseResources-40:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d41d36b14e47c257e85459df73d97ebd\\transformed\\media3-exoplayer-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,256,325,402,472,554,634", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "126,188,251,320,397,467,549,629,709"}, "to": {"startLines": "98,99,100,101,102,103,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7666,7742,7804,7867,7936,8013,8083,8165,8245", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "7737,7799,7862,7931,8008,8078,8160,8240,8320"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c077dfbc8dc20562e970eb684d92a281\\transformed\\media3-ui-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,610,924,1005,1085,1163,1265,1363,1441,1505,1594,1686,1756,1822,1887,1959,2072,2187,2310,2384,2464,2536,2617,2711,2806,2873,2938,2991,3049,3097,3158,3224,3291,3354,3421,3486,3545,3610,3674,3740,3792,3855,3932,4009", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "282,605,919,1000,1080,1158,1260,1358,1436,1500,1589,1681,1751,1817,1882,1954,2067,2182,2305,2379,2459,2531,2612,2706,2801,2868,2933,2986,3044,3092,3153,3219,3286,3349,3416,3481,3540,3605,3669,3735,3787,3850,3927,4004,4058"}, "to": {"startLines": "2,11,17,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,705,5652,5733,5813,5891,5993,6091,6169,6233,6322,6414,6484,6550,6615,6687,6800,6915,7038,7112,7192,7264,7345,7439,7534,7601,8325,8378,8436,8484,8545,8611,8678,8741,8808,8873,8932,8997,9061,9127,9179,9242,9319,9396", "endLines": "10,16,22,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "377,700,1014,5728,5808,5886,5988,6086,6164,6228,6317,6409,6479,6545,6610,6682,6795,6910,7033,7107,7187,7259,7340,7434,7529,7596,7661,8373,8431,8479,8540,8606,8673,8736,8803,8868,8927,8992,9056,9122,9174,9237,9314,9391,9445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c6ed593d095e40b444f65ff3ae72d38a\\transformed\\material-1.10.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1160,1259,1335,1396,1485,1549,1616,1670,1738,1798,1852,1969,2029,2091,2145,2217,2339,2423,2515,2652,2730,2812,2939,3027,3107,3161,3212,3278,3350,3427,3511,3592,3664,3741,3815,3886,3991,4079,4150,4243,4338,4412,4486,4582,4634,4717,4784,4870,4958,5020,5084,5147,5215,5325,5431,5530,5644,5702,5757", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,126,87,79,53,50,65,71,76,83,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1155,1254,1330,1391,1480,1544,1611,1665,1733,1793,1847,1964,2024,2086,2140,2212,2334,2418,2510,2647,2725,2807,2934,3022,3102,3156,3207,3273,3345,3422,3506,3587,3659,3736,3810,3881,3986,4074,4145,4238,4333,4407,4481,4577,4629,4712,4779,4865,4953,5015,5079,5142,5210,5320,5426,5525,5639,5697,5752,5831"}, "to": {"startLines": "23,57,58,59,60,61,69,70,71,73,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1019,4055,4133,4211,4288,4391,5214,5306,5432,5587,9450,9549,9625,9686,9775,9839,9906,9960,10028,10088,10142,10259,10319,10381,10435,10507,11133,11217,11309,11446,11524,11606,11733,11821,11901,11955,12006,12072,12144,12221,12305,12386,12458,12535,12609,12680,12785,12873,12944,13037,13132,13206,13280,13376,13428,13511,13578,13664,13752,13814,13878,13941,14009,14119,14225,14324,14438,14496,14551", "endLines": "28,57,58,59,60,61,69,70,71,73,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,126,87,79,53,50,65,71,76,83,80,71,76,73,70,104,87,70,92,94,73,73,95,51,82,66,85,87,61,63,62,67,109,105,98,113,57,54,78", "endOffsets": "1282,4128,4206,4283,4386,4478,5301,5427,5508,5647,9544,9620,9681,9770,9834,9901,9955,10023,10083,10137,10254,10314,10376,10430,10502,10624,11212,11304,11441,11519,11601,11728,11816,11896,11950,12001,12067,12139,12216,12300,12381,12453,12530,12604,12675,12780,12868,12939,13032,13127,13201,13275,13371,13423,13506,13573,13659,13747,13809,13873,13936,14004,14114,14220,14319,14433,14491,14546,14625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e355fbe8fe59cc5407000c654ab4b66\\transformed\\appcompat-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1287,1394,1496,1606,1692,1797,1914,1992,2068,2159,2252,2347,2441,2535,2628,2723,2820,2911,3002,3086,3190,3302,3401,3507,3618,3720,3883,14630", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "1389,1491,1601,1687,1792,1909,1987,2063,2154,2247,2342,2436,2530,2623,2718,2815,2906,2997,3081,3185,3297,3396,3502,3613,3715,3878,3976,14708"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8c6e034149c65399529a9cd0f184f8b\\transformed\\media3-session-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,129,203,276,345,425,509,604", "endColumns": "73,73,72,68,79,83,94,102", "endOffsets": "124,198,271,340,420,504,599,702"}, "to": {"startLines": "56,72,141,142,143,144,145,146", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3981,5513,10629,10702,10771,10851,10935,11030", "endColumns": "73,73,72,68,79,83,94,102", "endOffsets": "4050,5582,10697,10766,10846,10930,11025,11128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87c47c7abe7f29a54b096c7ff4d96851\\transformed\\core-1.16.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "62,63,64,65,66,67,68,191", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4483,4581,4683,4784,4883,4988,5095,14713", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "4576,4678,4779,4878,4983,5090,5209,14809"}}]}]}