android.app.Applicationandroid.os.Parcelable4com.nauh.musicplayer.contract.MainContract.Presenter6com.nauh.musicplayer.contract.PlayerContract.Presenter+androidx.media3.session.MediaSessionService-androidx.media3.session.MediaSession.Callback(androidx.appcompat.app.AppCompatActivity/com.nauh.musicplayer.contract.MainContract.View1com.nauh.musicplayer.contract.PlayerContract.View(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallbackIcom.nauh.musicplayer.service.MusicServiceConnection.PlaybackStateListener                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      