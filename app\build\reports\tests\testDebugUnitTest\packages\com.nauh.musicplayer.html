<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.nauh.musicplayer</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.nauh.musicplayer</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.nauh.musicplayer</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">11</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">2</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.062s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">81%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/com.nauh.musicplayer.MusicServiceTest.html">MusicServiceTest</a>.
<a href="../classes/com.nauh.musicplayer.MusicServiceTest.html#createMediaItem_withEmptyStreamUrl_throwsException">createMediaItem_withEmptyStreamUrl_throwsException</a>
</li>
<li>
<a href="../classes/com.nauh.musicplayer.MusicServiceTest.html">MusicServiceTest</a>.
<a href="../classes/com.nauh.musicplayer.MusicServiceTest.html#createMediaItem_withValidSong_isCorrect">createMediaItem_withValidSong_isCorrect</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="success">
<a href="../classes/com.nauh.musicplayer.ExampleUnitTest.html">ExampleUnitTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.003s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="../classes/com.nauh.musicplayer.MusicServiceTest.html">MusicServiceTest</a>
</td>
<td>2</td>
<td>2</td>
<td>0</td>
<td>0.021s</td>
<td class="failures">0%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.nauh.musicplayer.PlayerPresenterTest.html">PlayerPresenterTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.036s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.nauh.musicplayer.SongModelTest.html">SongModelTest</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>0.002s</td>
<td class="success">100%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at Jul 25, 2025, 8:06:54 PM</p>
</div>
</div>
</body>
</html>
