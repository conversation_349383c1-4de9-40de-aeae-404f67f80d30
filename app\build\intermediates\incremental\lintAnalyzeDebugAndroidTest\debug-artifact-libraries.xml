<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.test.ext:junit:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a40459c104dcd9d16f91e3b733f419\transformed\junit-1.2.1\jars\classes.jar"
      resolved="androidx.test.ext:junit:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1a40459c104dcd9d16f91e3b733f419\transformed\junit-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.espresso:espresso-core:3.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c9ddfe9a3bb9e4c810f3877c7c3d4bb\transformed\espresso-core-3.6.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-core:3.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c9ddfe9a3bb9e4c810f3877c7c3d4bb\transformed\espresso-core-3.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\2.0.21\8947a70750a8552acfe84ad5703e16bf072d9368\kotlin-parcelize-runtime-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21"/>
  <library
      name="com.google.android.material:material:1.10.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6ed593d095e40b444f65ff3ae72d38a\transformed\material-1.10.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.10.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6ed593d095e40b444f65ff3ae72d38a\transformed\material-1.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e355fbe8fe59cc5407000c654ab4b66\transformed\appcompat-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e355fbe8fe59cc5407000c654ab4b66\transformed\appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e174fbc34a0d4d67d705dfd2843177\transformed\glide-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.16.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e174fbc34a0d4d67d705dfd2843177\transformed\glide-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2eab8cce036af7f10c282d2960adad52\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2eab8cce036af7f10c282d2960adad52\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e874d518bf964cae10390a22d79224de\transformed\fragment-1.3.6\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e874d518bf964cae10390a22d79224de\transformed\fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a4e92105cf6be4572591f3ff65dd1bf\transformed\activity-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.10.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a4e92105cf6be4572591f3ff65dd1bf\transformed\activity-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:core:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50d3788d34c40a66b2b378828c7cb305\transformed\core-1.6.1\jars\classes.jar"
      resolved="androidx.test:core:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50d3788d34c40a66b2b378828c7cb305\transformed\core-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6fc2ce3f277b1fd7b9e37a053f40d4b\transformed\media-1.6.0\jars\classes.jar"
      resolved="androidx.media:media:1.6.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6fc2ce3f277b1fd7b9e37a053f40d4b\transformed\media-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.3.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7cc6f7c11c83b92d7f72847c7e0634f\transformed\recyclerview-1.3.2\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.3.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7cc6f7c11c83b92d7f72847c7e0634f\transformed\recyclerview-1.3.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f3fca723553bf78ddc1ceed4d64a281\transformed\swiperefreshlayout-1.1.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f3fca723553bf78ddc1ceed4d64a281\transformed\swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b17848eedbf18dc9b7e91e1865e4d673\transformed\appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b17848eedbf18dc9b7e91e1865e4d673\transformed\appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c8267f0e8b21b87e89f8a7c76d1604e\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c8267f0e8b21b87e89f8a7c76d1604e\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cec21f2898952ce5034941b8173673a\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cec21f2898952ce5034941b8173673a\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1433f273569648a2bd8146aa3b831c2f\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1433f273569648a2bd8146aa3b831c2f\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c42b13483616a49bec81c27148aec4c1\transformed\transition-1.2.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c42b13483616a49bec81c27148aec4c1\transformed\transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0a43d0ddc5584b5188c82d7d0030a9b\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0a43d0ddc5584b5188c82d7d0030a9b\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cea338972f4a00559da092b5a8680dd2\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cea338972f4a00559da092b5a8680dd2\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28dd29f78fdfedd1675dc1cdda8bb25\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28dd29f78fdfedd1675dc1cdda8bb25\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96126a5e4749013cbacff2a64007ed37\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96126a5e4749013cbacff2a64007ed37\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\935e14ac57f83777c2201c0cf85a584a\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\935e14ac57f83777c2201c0cf85a584a\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a965412fe5151442b2bfa797ebf181cd\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a965412fe5151442b2bfa797ebf181cd\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\jars\classes.jar"
      resolved="androidx.core:core:1.16.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcb31f7a052a2aa1cc4d8ad1fec665b4\transformed\lifecycle-livedata-core-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcb31f7a052a2aa1cc4d8ad1fec665b4\transformed\lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adabb7a03ca92389e565c98428b524a\transformed\lifecycle-livedata-core-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adabb7a03ca92389e565c98428b524a\transformed\lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\848eb237232ce40e10f8dd56810eeea8\transformed\lifecycle-livedata-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\848eb237232ce40e10f8dd56810eeea8\transformed\lifecycle-livedata-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872c24cbb96c2fb131dc7e738cf8bf51\transformed\lifecycle-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872c24cbb96c2fb131dc7e738cf8bf51\transformed\lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\367465219d0b507e8dc9dbd1a5344456\transformed\lifecycle-viewmodel-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\367465219d0b507e8dc9dbd1a5344456\transformed\lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.7.0\85334205d65cca70ed0109c3acbd29e22a2d9cb1\lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7497663364c819353feb4dad02d95ff7\transformed\lifecycle-livedata-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-ktx:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7497663364c819353feb4dad02d95ff7\transformed\lifecycle-livedata-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3178617f332ad6be1ea5ea9e7465c76\transformed\lifecycle-viewmodel-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3178617f332ad6be1ea5ea9e7465c76\transformed\lifecycle-viewmodel-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3763dc498a53800ae6508ded1a7e386\transformed\lifecycle-viewmodel-savedstate-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3763dc498a53800ae6508ded1a7e386\transformed\lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457fc5db20d28928c9960c9d622a1da4\transformed\core-ktx-1.16.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.16.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457fc5db20d28928c9960c9d622a1da4\transformed\core-ktx-1.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:runner:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d760a52261c5d8eac425a6e9149bb9f\transformed\runner-1.6.1\jars\classes.jar"
      resolved="androidx.test:runner:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d760a52261c5d8eac425a6e9149bb9f\transformed\runner-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.services:storage:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8817c67d8b6a28a2a8277c2c7b287167\transformed\storage-1.5.0\jars\classes.jar"
      resolved="androidx.test.services:storage:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8817c67d8b6a28a2a8277c2c7b287167\transformed\storage-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:monitor:1.7.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c46029a66f6933107ccd5cb59884566\transformed\monitor-1.7.1\jars\classes.jar"
      resolved="androidx.test:monitor:1.7.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c46029a66f6933107ccd5cb59884566\transformed\monitor-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\2.0.21\ec0f8b769af81cd91a76b530399684a9567bdc35\kotlin-android-extensions-runtime-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26d39a5b70991aa80152a68678aef9f5\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26d39a5b70991aa80152a68678aef9f5\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7036c2a2d7e4ca9ecb8c6b9000a9520d\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7036c2a2d7e4ca9ecb8c6b9000a9520d\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"
      provided="true"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.1.0\b4c245baf36d1a9e7defaf3be84f7a2ad4e1c797\concurrent-futures-ktx-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.1.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.9.0\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\converter-gson-2.9.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.9.0"
      provided="true"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.9.0\d8fdfbd5da952141a665a403348b74538efc05ff\retrofit-2.9.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.9.0"
      provided="true"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"
      provided="true"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae7bfe8c8a1006908786b3d7e3ea5a3a\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae7bfe8c8a1006908786b3d7e3ea5a3a\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6af5882327e1616896d9970274b3fd43\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6af5882327e1616896d9970274b3fd43\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a9130932b87029b221963c70cfe0bd8\transformed\gifdecoder-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.16.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a9130932b87029b221963c70cfe0bd8\transformed\gifdecoder-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3763be5c53eced13efcc6a30735bb63\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3763be5c53eced13efcc6a30735bb63\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29346d34bf1b1083dfd3df8394df3249\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29346d34bf1b1083dfd3df8394df3249\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"
      provided="true"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506bded0489842502ec9da7deddacd43\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506bded0489842502ec9da7deddacd43\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"
      provided="true"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c3ef6fd099ae0f56620debe136b0337\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c3ef6fd099ae0f56620debe136b0337\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f4883560e1ff6314b0495f687a8e929\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f4883560e1ff6314b0495f687a8e929\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c7d05e18a7c1d49e2325ff82921ca3f\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c7d05e18a7c1d49e2325ff82921ca3f\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.1\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="androidx.media3:media3-exoplayer:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d41d36b14e47c257e85459df73d97ebd\transformed\media3-exoplayer-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d41d36b14e47c257e85459df73d97ebd\transformed\media3-exoplayer-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-session:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c6e034149c65399529a9cd0f184f8b\transformed\media3-session-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-session:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c6e034149c65399529a9cd0f184f8b\transformed\media3-session-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-common:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\629cdf61503b301df7d941af34c8ac0c\transformed\media3-common-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-common:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\629cdf61503b301df7d941af34c8ac0c\transformed\media3-common-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b14c28b16bd0529396929de31ff982\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b14c28b16bd0529396929de31ff982\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c993b0c7ed05af12615540a976a5d5f9\transformed\constraintlayout-2.1.4\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.1.4"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c993b0c7ed05af12615540a976a5d5f9\transformed\constraintlayout-2.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-ui:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c077dfbc8dc20562e970eb684d92a281\transformed\media3-ui-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-ui:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c077dfbc8dc20562e970eb684d92a281\transformed\media3-ui-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:guava:31.1-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\31.1-android\9222c47cc3ae890f07f7c961bbb3cb69050fe4aa\guava-31.1-android.jar"
      resolved="com.google.guava:guava:31.1-android"
      provided="true"/>
  <library
      name="de.hdodenhof:circleimageview:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9809c659558a636288a9501c9f0fd8ae\transformed\circleimageview-3.1.0\jars\classes.jar"
      resolved="de.hdodenhof:circleimageview:3.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9809c659558a636288a9501c9f0fd8ae\transformed\circleimageview-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24e75a0eaadbec175571014fe7d18893\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24e75a0eaadbec175571014fe7d18893\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-library:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar"
      resolved="org.hamcrest:hamcrest-library:1.3"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="androidx.test.espresso:espresso-idling-resource:3.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ad49563c2d716b2aa1cc831f1d57900\transformed\espresso-idling-resource-3.6.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-idling-resource:3.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4ad49563c2d716b2aa1cc831f1d57900\transformed\espresso-idling-resource-3.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.15.0\38c8485a652f808c8c149150da4e5c2b0bd17f9a\error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"
      provided="true"/>
  <library
      name="androidx.media3:media3-container:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\118f6d7e5615fddd38c13715daca2f39\transformed\media3-container-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-container:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\118f6d7e5615fddd38c13715daca2f39\transformed\media3-container-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-datasource:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ab1aa8a50e2548aa233320259c2efee\transformed\media3-datasource-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-datasource:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ab1aa8a50e2548aa233320259c2efee\transformed\media3-datasource-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-decoder:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fff4b4ab9079b744105d347195f936b0\transformed\media3-decoder-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-decoder:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fff4b4ab9079b744105d347195f936b0\transformed\media3-decoder-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-extractor:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9675887e93f9a618c9b0c8c4d8f5d2a\transformed\media3-extractor-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-extractor:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9675887e93f9a618c9b0c8c4d8f5d2a\transformed\media3-extractor-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-database:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba02c9976259f0e4af95e1788ef6707\transformed\media3-database-1.2.1\jars\classes.jar"
      resolved="androidx.media3:media3-database:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba02c9976259f0e4af95e1788ef6707\transformed\media3-database-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"
      provided="true"/>
  <library
      name="org.checkerframework:checker-qual:3.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.12.0\d5692f0526415fcc6de94bb5bfbd3afd9dd3b3e5\checker-qual-3.12.0.jar"
      resolved="org.checkerframework:checker-qual:3.12.0"
      provided="true"/>
  <library
      name="com.google.j2objc:j2objc-annotations:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\1.3\ba035118bc8bac37d7eff77700720999acd9986d\j2objc-annotations-1.3.jar"
      resolved="com.google.j2objc:j2objc-annotations:1.3"
      provided="true"/>
  <library
      name="com.google.code.gson:gson:2.8.5@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.8.5\f645ed69d595b24d4cf8b3fbb64cc505bede8829\gson-2.8.5.jar"
      resolved="com.google.code.gson:gson:2.8.5"
      provided="true"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.16.0\411aa175d50d10b37c7a1a04d21a4e7145249557\disklrucache-4.16.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.16.0"
      provided="true"/>
  <library
      name="com.github.bumptech.glide:annotations:4.16.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.16.0\90730f6498299d207aa0878124ab7585969808f0\annotations-4.16.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.16.0"
      provided="true"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c11b935831517bbe86674c845ce9659b\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c11b935831517bbe86674c845ce9659b\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"
      provided="true"/>
</libraries>
