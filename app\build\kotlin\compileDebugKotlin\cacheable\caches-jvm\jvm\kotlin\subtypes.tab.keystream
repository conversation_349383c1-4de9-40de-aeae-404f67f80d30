android.app.Applicationandroid.os.Parcelable4com.nauh.musicplayer.contract.MainContract.Presenter6com.nauh.musicplayer.contract.PlayerContract.PresenterIcom.nauh.musicplayer.service.MusicServiceConnection.PlaybackStateListener+androidx.media3.session.MediaSessionService(androidx.appcompat.app.AppCompatActivity/com.nauh.musicplayer.contract.MainContract.View1com.nauh.musicplayer.contract.PlayerContract.View(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallback-androidx.media3.session.MediaSession.Callback                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      