-- Merging decision tree log ---
manifest
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:2:1-51:12
INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:2:1-51:12
INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:2:1-51:12
INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:2:1-51:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6ed593d095e40b444f65ff3ae72d38a\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c993b0c7ed05af12615540a976a5d5f9\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b17848eedbf18dc9b7e91e1865e4d673\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e355fbe8fe59cc5407000c654ab4b66\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e174fbc34a0d4d67d705dfd2843177\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d41d36b14e47c257e85459df73d97ebd\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c6e034149c65399529a9cd0f184f8b\transformed\media3-session-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9675887e93f9a618c9b0c8c4d8f5d2a\transformed\media3-extractor-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\118f6d7e5615fddd38c13715daca2f39\transformed\media3-container-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ab1aa8a50e2548aa233320259c2efee\transformed\media3-datasource-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fff4b4ab9079b744105d347195f936b0\transformed\media3-decoder-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba02c9976259f0e4af95e1788ef6707\transformed\media3-database-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\629cdf61503b301df7d941af34c8ac0c\transformed\media3-common-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c077dfbc8dc20562e970eb684d92a281\transformed\media3-ui-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7cc6f7c11c83b92d7f72847c7e0634f\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22f072afa754996903b451af3c838d02\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e874d518bf964cae10390a22d79224de\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a4e92105cf6be4572591f3ff65dd1bf\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6fc2ce3f277b1fd7b9e37a053f40d4b\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8eb209fce1e11dfb078cc21ad59fcbc8\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c8267f0e8b21b87e89f8a7c76d1604e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cec21f2898952ce5034941b8173673a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1433f273569648a2bd8146aa3b831c2f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c42b13483616a49bec81c27148aec4c1\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0a43d0ddc5584b5188c82d7d0030a9b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cea338972f4a00559da092b5a8680dd2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28dd29f78fdfedd1675dc1cdda8bb25\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96126a5e4749013cbacff2a64007ed37\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\935e14ac57f83777c2201c0cf85a584a\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a965412fe5151442b2bfa797ebf181cd\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ed32aea4e2a58485cc6d67a05ada220\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26d39a5b70991aa80152a68678aef9f5\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcb31f7a052a2aa1cc4d8ad1fec665b4\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adabb7a03ca92389e565c98428b524a\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\848eb237232ce40e10f8dd56810eeea8\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872c24cbb96c2fb131dc7e738cf8bf51\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\367465219d0b507e8dc9dbd1a5344456\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7497663364c819353feb4dad02d95ff7\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3178617f332ad6be1ea5ea9e7465c76\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3763dc498a53800ae6508ded1a7e386\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457fc5db20d28928c9960c9d622a1da4\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7036c2a2d7e4ca9ecb8c6b9000a9520d\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae7bfe8c8a1006908786b3d7e3ea5a3a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6af5882327e1616896d9970274b3fd43\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c11b935831517bbe86674c845ce9659b\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a9130932b87029b221963c70cfe0bd8\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d47c4b7d0a3173289d09914c71be7f0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24e75a0eaadbec175571014fe7d18893\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3763be5c53eced13efcc6a30735bb63\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29346d34bf1b1083dfd3df8394df3249\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506bded0489842502ec9da7deddacd43\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c3ef6fd099ae0f56620debe136b0337\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f4883560e1ff6314b0495f687a8e929\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c7d05e18a7c1d49e2325ff82921ca3f\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b14c28b16bd0529396929de31ff982\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d41d36b14e47c257e85459df73d97ebd\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d41d36b14e47c257e85459df73d97ebd\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\629cdf61503b301df7d941af34c8ac0c\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\629cdf61503b301df7d941af34c8ac0c\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:8:5-68
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:8:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:9:5-77
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:9:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:10:5-92
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:10:22-89
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:11:5-77
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:11:22-74
application
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:13:5-49:19
INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:13:5-49:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6ed593d095e40b444f65ff3ae72d38a\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6ed593d095e40b444f65ff3ae72d38a\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c993b0c7ed05af12615540a976a5d5f9\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c993b0c7ed05af12615540a976a5d5f9\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d47c4b7d0a3173289d09914c71be7f0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d47c4b7d0a3173289d09914c71be7f0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29346d34bf1b1083dfd3df8394df3249\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29346d34bf1b1083dfd3df8394df3249\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:21:9-35
	android:label
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:19:9-41
	android:fullBackupContent
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:17:9-54
	android:roundIcon
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:20:9-54
	tools:targetApi
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:24:9-29
	android:icon
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:18:9-43
	android:allowBackup
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:15:9-35
	android:theme
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:22:9-49
	android:dataExtractionRules
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:16:9-65
	android:usesCleartextTraffic
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:23:9-44
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:14:9-47
activity#com.nauh.musicplayer.ui.MainActivity
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:25:9-33:20
	android:exported
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:27:13-36
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:26:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:28:13-32:29
action#android.intent.action.MAIN
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:29:17-69
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:31:17-77
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:31:27-74
activity#com.nauh.musicplayer.ui.PlayerActivity
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:35:9-38:52
	android:screenOrientation
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:38:13-49
	android:exported
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:37:13-37
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:36:13-46
service#com.nauh.musicplayer.service.MusicService
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:41:9-48:19
	android:exported
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:43:13-37
	android:foregroundServiceType
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:44:13-58
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:42:13-49
intent-filter#action:name:androidx.media3.session.MediaSessionService
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:45:13-47:29
action#androidx.media3.session.MediaSessionService
ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:46:17-86
	android:name
		ADDED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:46:25-83
uses-sdk
INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml
INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6ed593d095e40b444f65ff3ae72d38a\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6ed593d095e40b444f65ff3ae72d38a\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c993b0c7ed05af12615540a976a5d5f9\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c993b0c7ed05af12615540a976a5d5f9\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b17848eedbf18dc9b7e91e1865e4d673\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b17848eedbf18dc9b7e91e1865e4d673\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e355fbe8fe59cc5407000c654ab4b66\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e355fbe8fe59cc5407000c654ab4b66\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e174fbc34a0d4d67d705dfd2843177\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\05e174fbc34a0d4d67d705dfd2843177\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d41d36b14e47c257e85459df73d97ebd\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d41d36b14e47c257e85459df73d97ebd\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c6e034149c65399529a9cd0f184f8b\transformed\media3-session-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8c6e034149c65399529a9cd0f184f8b\transformed\media3-session-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9675887e93f9a618c9b0c8c4d8f5d2a\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9675887e93f9a618c9b0c8c4d8f5d2a\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\118f6d7e5615fddd38c13715daca2f39\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\118f6d7e5615fddd38c13715daca2f39\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ab1aa8a50e2548aa233320259c2efee\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ab1aa8a50e2548aa233320259c2efee\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fff4b4ab9079b744105d347195f936b0\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fff4b4ab9079b744105d347195f936b0\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba02c9976259f0e4af95e1788ef6707\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ba02c9976259f0e4af95e1788ef6707\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\629cdf61503b301df7d941af34c8ac0c\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\629cdf61503b301df7d941af34c8ac0c\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c077dfbc8dc20562e970eb684d92a281\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c077dfbc8dc20562e970eb684d92a281\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7cc6f7c11c83b92d7f72847c7e0634f\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7cc6f7c11c83b92d7f72847c7e0634f\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22f072afa754996903b451af3c838d02\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22f072afa754996903b451af3c838d02\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e874d518bf964cae10390a22d79224de\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e874d518bf964cae10390a22d79224de\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a4e92105cf6be4572591f3ff65dd1bf\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a4e92105cf6be4572591f3ff65dd1bf\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6fc2ce3f277b1fd7b9e37a053f40d4b\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6fc2ce3f277b1fd7b9e37a053f40d4b\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8eb209fce1e11dfb078cc21ad59fcbc8\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8eb209fce1e11dfb078cc21ad59fcbc8\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c8267f0e8b21b87e89f8a7c76d1604e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c8267f0e8b21b87e89f8a7c76d1604e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cec21f2898952ce5034941b8173673a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cec21f2898952ce5034941b8173673a\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1433f273569648a2bd8146aa3b831c2f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1433f273569648a2bd8146aa3b831c2f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c42b13483616a49bec81c27148aec4c1\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c42b13483616a49bec81c27148aec4c1\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0a43d0ddc5584b5188c82d7d0030a9b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0a43d0ddc5584b5188c82d7d0030a9b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cea338972f4a00559da092b5a8680dd2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cea338972f4a00559da092b5a8680dd2\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28dd29f78fdfedd1675dc1cdda8bb25\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a28dd29f78fdfedd1675dc1cdda8bb25\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96126a5e4749013cbacff2a64007ed37\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96126a5e4749013cbacff2a64007ed37\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\935e14ac57f83777c2201c0cf85a584a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\935e14ac57f83777c2201c0cf85a584a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a965412fe5151442b2bfa797ebf181cd\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a965412fe5151442b2bfa797ebf181cd\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ed32aea4e2a58485cc6d67a05ada220\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ed32aea4e2a58485cc6d67a05ada220\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26d39a5b70991aa80152a68678aef9f5\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26d39a5b70991aa80152a68678aef9f5\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcb31f7a052a2aa1cc4d8ad1fec665b4\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcb31f7a052a2aa1cc4d8ad1fec665b4\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adabb7a03ca92389e565c98428b524a\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4adabb7a03ca92389e565c98428b524a\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\848eb237232ce40e10f8dd56810eeea8\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\848eb237232ce40e10f8dd56810eeea8\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872c24cbb96c2fb131dc7e738cf8bf51\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\872c24cbb96c2fb131dc7e738cf8bf51\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\367465219d0b507e8dc9dbd1a5344456\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\367465219d0b507e8dc9dbd1a5344456\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7497663364c819353feb4dad02d95ff7\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7497663364c819353feb4dad02d95ff7\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3178617f332ad6be1ea5ea9e7465c76\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3178617f332ad6be1ea5ea9e7465c76\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3763dc498a53800ae6508ded1a7e386\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3763dc498a53800ae6508ded1a7e386\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457fc5db20d28928c9960c9d622a1da4\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\457fc5db20d28928c9960c9d622a1da4\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7036c2a2d7e4ca9ecb8c6b9000a9520d\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7036c2a2d7e4ca9ecb8c6b9000a9520d\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae7bfe8c8a1006908786b3d7e3ea5a3a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae7bfe8c8a1006908786b3d7e3ea5a3a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6af5882327e1616896d9970274b3fd43\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6af5882327e1616896d9970274b3fd43\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c11b935831517bbe86674c845ce9659b\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c11b935831517bbe86674c845ce9659b\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a9130932b87029b221963c70cfe0bd8\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a9130932b87029b221963c70cfe0bd8\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d47c4b7d0a3173289d09914c71be7f0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d47c4b7d0a3173289d09914c71be7f0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24e75a0eaadbec175571014fe7d18893\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24e75a0eaadbec175571014fe7d18893\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3763be5c53eced13efcc6a30735bb63\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f3763be5c53eced13efcc6a30735bb63\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29346d34bf1b1083dfd3df8394df3249\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29346d34bf1b1083dfd3df8394df3249\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506bded0489842502ec9da7deddacd43\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\506bded0489842502ec9da7deddacd43\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c3ef6fd099ae0f56620debe136b0337\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c3ef6fd099ae0f56620debe136b0337\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f4883560e1ff6314b0495f687a8e929\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3f4883560e1ff6314b0495f687a8e929\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c7d05e18a7c1d49e2325ff82921ca3f\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c7d05e18a7c1d49e2325ff82921ca3f\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b14c28b16bd0529396929de31ff982\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6b14c28b16bd0529396929de31ff982\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d47c4b7d0a3173289d09914c71be7f0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d47c4b7d0a3173289d09914c71be7f0f\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.nauh.musicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.nauh.musicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
