<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.nauh.musicplayer.PlayerPresenterTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-07-25T12:32:22" hostname="DESKTOP-6NA0Q4V" time="0.046">
  <properties/>
  <testcase name="onProgressUpdate_shouldUpdateProgressAndSeekBar" classname="com.nauh.musicplayer.PlayerPresenterTest" time="0.039"/>
  <testcase name="onPlaybackStateChanged_shouldUpdatePlayPauseButton" classname="com.nauh.musicplayer.PlayerPresenterTest" time="0.004"/>
  <testcase name="onSongChanged_shouldUpdateView" classname="com.nauh.musicplayer.PlayerPresenterTest" time="0.0"/>
  <testcase name="initializePlayer_shouldShowSongInfo" classname="com.nauh.musicplayer.PlayerPresenterTest" time="0.001"/>
  <testcase name="toggleShuffle_shouldUpdateShuffleState" classname="com.nauh.musicplayer.PlayerPresenterTest" time="0.0"/>
  <testcase name="toggleRepeat_shouldCycleThroughRepeatModes" classname="com.nauh.musicplayer.PlayerPresenterTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
