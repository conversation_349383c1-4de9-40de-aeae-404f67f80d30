{"logs": [{"outputFile": "com.nauh.musicplayer.app-mergeReleaseResources-40:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8c6e034149c65399529a9cd0f184f8b\\transformed\\media3-session-1.2.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,131,205,273,339,412,494,584", "endColumns": "75,73,67,65,72,81,89,96", "endOffsets": "126,200,268,334,407,489,579,676"}, "to": {"startLines": "46,54,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3363,4158,7896,7964,8030,8103,8185,8275", "endColumns": "75,73,67,65,72,81,89,96", "endOffsets": "3434,4227,7959,8025,8098,8180,8270,8367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c077dfbc8dc20562e970eb684d92a281\\transformed\\media3-ui-1.2.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,656,738,820,898,985,1070,1137,1200,1292,1384,1449,1512,1574,1645,1755,1866,1976,2043,2123,2194,2261,2346,2431,2494,2558,2611,2669,2717,2778,2843,2905,2970,3041,3099,3157,3223,3287,3353,3405,3467,3543,3619", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,733,815,893,980,1065,1132,1195,1287,1379,1444,1507,1569,1640,1750,1861,1971,2038,2118,2189,2256,2341,2426,2489,2553,2606,2664,2712,2773,2838,2900,2965,3036,3094,3152,3218,3282,3348,3400,3462,3538,3614,3668"}, "to": {"startLines": "2,11,15,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,529,4232,4314,4396,4474,4561,4646,4713,4776,4868,4960,5025,5088,5150,5221,5331,5442,5552,5619,5699,5770,5837,5922,6007,6070,6781,6834,6892,6940,7001,7066,7128,7193,7264,7322,7380,7446,7510,7576,7628,7690,7766,7842", "endLines": "10,14,18,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "330,524,701,4309,4391,4469,4556,4641,4708,4771,4863,4955,5020,5083,5145,5216,5326,5437,5547,5614,5694,5765,5832,5917,6002,6065,6129,6829,6887,6935,6996,7061,7123,7188,7259,7317,7375,7441,7505,7571,7623,7685,7761,7837,7891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e355fbe8fe59cc5407000c654ab4b66\\transformed\\appcompat-1.6.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "706,810,910,1018,1102,1202,1317,1395,1470,1561,1654,1749,1843,1943,2036,2131,2225,2316,2407,2489,2592,2695,2794,2899,3003,3107,3263,8372", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "805,905,1013,1097,1197,1312,1390,1465,1556,1649,1744,1838,1938,2031,2126,2220,2311,2402,2484,2587,2690,2789,2894,2998,3102,3258,3358,8450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d41d36b14e47c257e85459df73d97ebd\\transformed\\media3-exoplayer-1.2.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6134,6204,6266,6331,6395,6472,6537,6627,6712", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "6199,6261,6326,6390,6467,6532,6622,6707,6776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87c47c7abe7f29a54b096c7ff4d96851\\transformed\\core-1.16.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "47,48,49,50,51,52,53,113", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3439,3535,3637,3736,3835,3939,4042,8455", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3530,3632,3731,3830,3934,4037,4153,8551"}}]}]}