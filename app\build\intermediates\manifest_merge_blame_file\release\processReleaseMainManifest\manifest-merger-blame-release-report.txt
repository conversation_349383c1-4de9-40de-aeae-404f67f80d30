1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.nauh.musicplayer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions for music streaming -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:8:5-68
14-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:8:22-65
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:9:5-77
15-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
16-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:10:5-92
16-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:10:22-89
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:11:5-77
17-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:11:22-74
18
19    <permission
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.nauh.musicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.nauh.musicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
24
25    <application
25-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:13:5-49:19
26        android:name="com.nauh.musicplayer.MusicPlayerApplication"
26-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:14:9-47
27        android:allowBackup="true"
27-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:15:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
29        android:dataExtractionRules="@xml/data_extraction_rules"
29-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:16:9-65
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:17:9-54
32        android:icon="@mipmap/ic_launcher"
32-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:18:9-43
33        android:label="@string/app_name"
33-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:19:9-41
34        android:roundIcon="@mipmap/ic_launcher_round"
34-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:20:9-54
35        android:supportsRtl="true"
35-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:21:9-35
36        android:theme="@style/Theme.MusicPlayer"
36-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:22:9-49
37        android:usesCleartextTraffic="true" >
37-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:23:9-44
38        <activity
38-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:25:9-33:20
39            android:name="com.nauh.musicplayer.ui.MainActivity"
39-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:26:13-44
40            android:exported="true" >
40-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:27:13-36
41            <intent-filter>
41-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:28:13-32:29
42                <action android:name="android.intent.action.MAIN" />
42-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:29:17-69
42-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:29:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:31:17-77
44-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:31:27-74
45            </intent-filter>
46        </activity>
47        <activity
47-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:35:9-38:52
48            android:name="com.nauh.musicplayer.ui.PlayerActivity"
48-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:36:13-46
49            android:exported="false"
49-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:37:13-37
50            android:screenOrientation="portrait" />
50-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:38:13-49
51
52        <!-- Music Service -->
53        <service
53-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:41:9-48:19
54            android:name="com.nauh.musicplayer.service.MusicService"
54-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:42:13-49
55            android:exported="false"
55-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:43:13-37
56            android:foregroundServiceType="mediaPlayback" >
56-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:44:13-58
57            <intent-filter>
57-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:45:13-47:29
58                <action android:name="androidx.media3.session.MediaSessionService" />
58-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:46:17-86
58-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:46:25-83
59            </intent-filter>
60        </service>
61
62        <provider
62-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
63            android:name="androidx.startup.InitializationProvider"
63-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
64            android:authorities="com.nauh.musicplayer.androidx-startup"
64-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
65            android:exported="false" >
65-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
66            <meta-data
66-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.emoji2.text.EmojiCompatInitializer"
67-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
68                android:value="androidx.startup" />
68-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
70-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
71                android:value="androidx.startup" />
71-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
72            <meta-data
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
74                android:value="androidx.startup" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
75        </provider>
76
77        <receiver
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
78            android:name="androidx.profileinstaller.ProfileInstallReceiver"
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
79            android:directBootAware="false"
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
80            android:enabled="true"
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
81            android:exported="true"
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
82            android:permission="android.permission.DUMP" >
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
84                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
87                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
90                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
93                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
94            </intent-filter>
95        </receiver>
96    </application>
97
98</manifest>
