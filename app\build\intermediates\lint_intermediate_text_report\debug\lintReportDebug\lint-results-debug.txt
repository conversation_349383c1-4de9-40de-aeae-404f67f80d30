D:\Android Projects\MusicPlayer\app\src\main\java\com\nauh\musicplayer\service\MusicService.kt:148: Error: Overriding method should call super.onStartCommand [MissingSuperCall]
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
                 ~~~~~~~~~~~~~~

   Explanation for issues of type "MissingSuperCall":
   Some methods, such as View#onDetachedFromWindow, require that you also call
   the super implementation as part of your method.

D:\Android Projects\MusicPlayer\app\src\main\res\layout\mini_player.xml:3: Error: In Gradle projects, always use http://schemas.android.com/apk/res-auto for custom attributes [ResAuto]
    xmlns:app="http://schemas.android.com/apk/res/auto"
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ResAuto":
   In Gradle projects, the actual package used in the final APK can vary; for
   example,you can add a .debug package suffix in one version and not the
   other. Therefore, you should not hardcode the application package in the
   resource; instead, use the special namespace
   http://schemas.android.com/apk/res-auto which will cause the tools to
   figure out the right namespace for the resource regardless of the actual
   package used during the build.

D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:12: Warning: READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: READ_MEDIA_IMAGES, READ_MEDIA_VIDEO or READ_MEDIA_AUDIO. [ScopedStorage]
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScopedStorage":
   Scoped storage is enforced on Android 10+ (or Android 11+ if using
   requestLegacyExternalStorage). In particular, WRITE_EXTERNAL_STORAGE will
   no longer provide write access to all files; it will provide the equivalent
   of READ_EXTERNAL_STORAGE instead.

   As of Android 13, if you need to query or interact with MediaStore or media
   files on the shared storage, you should be using instead one or more new
   storage permissions:
   * android.permission.READ_MEDIA_IMAGES
   * android.permission.READ_MEDIA_VIDEO
   * android.permission.READ_MEDIA_AUDIO

   and then add maxSdkVersion="33" to the older permission. See the developer
   guide for how to do this:
   https://developer.android.com/about/versions/13/behavior-changes-13#granula
   r-media-permissions

   The MANAGE_EXTERNAL_STORAGE permission can be used to manage all files, but
   it is rarely necessary and most apps on Google Play are not allowed to use
   it. Most apps should instead migrate to use scoped storage. To modify or
   delete files, apps should request write access from the user as described
   at https://goo.gle/android-mediastore-createwriterequest.

   To learn more, read these resources: Play policy:
   https://goo.gle/policy-storage-help Allowable use cases:
   https://goo.gle/policy-storage-usecases

   https://goo.gle/android-storage-usecases

D:\Android Projects\MusicPlayer\app\src\main\java\com\nauh\musicplayer\ui\PlayerActivity.kt:233: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("%02d:%02d", minutes, seconds)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\java\com\nauh\musicplayer\data\model\Playlist.kt:36: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            String.format("%02d:%02d:%02d", hours, minutes, seconds)
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\java\com\nauh\musicplayer\data\model\Playlist.kt:38: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            String.format("%02d:%02d", minutes, seconds)
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\java\com\nauh\musicplayer\data\model\Song.kt:29: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("%02d:%02d", minutes, seconds)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

D:\Android Projects\MusicPlayer\app\src\main\java\com\nauh\musicplayer\service\MusicServiceConnection.kt:77: Error: Call requires API level 28 (current min is 24): android.content.Context#getMainExecutor [NewApi]
        }, context.mainExecutor)
                   ~~~~~~~~~~~~

   Explanation for issues of type "NewApi":
   This check scans through all the Android API calls in the application and
   warns about any calls that are not available on all versions targeted by
   this application (according to its minimum SDK attribute in the manifest).

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

   If you are deliberately setting android: attributes in style definitions,
   make sure you place this in a values-vNN folder in order to avoid running
   into runtime conflicts on certain devices where manufacturers have added
   custom attributes whose ids conflict with the new ones on later platforms.

   Similarly, you can use tools:targetApi="11" in an XML file to indicate that
   the element will only be inflated in an adequate context.

D:\Android Projects\MusicPlayer\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.10.1 is available: 8.11.1 [AndroidGradlePluginVersion]
agp = "8.10.1"
      ~~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

D:\Android Projects\MusicPlayer\app\build.gradle.kts:48: Warning: A newer version of androidx.media3:media3-exoplayer than 1.2.1 is available: 1.7.1 [GradleDependency]
    implementation("androidx.media3:media3-exoplayer:1.2.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:49: Warning: A newer version of androidx.media3:media3-ui than 1.2.1 is available: 1.7.1 [GradleDependency]
    implementation("androidx.media3:media3-ui:1.2.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:50: Warning: A newer version of androidx.media3:media3-session than 1.2.1 is available: 1.7.1 [GradleDependency]
    implementation("androidx.media3:media3-session:1.2.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:51: Warning: A newer version of androidx.media3:media3-common than 1.2.1 is available: 1.7.1 [GradleDependency]
    implementation("androidx.media3:media3-common:1.2.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:54: Warning: A newer version of androidx.media:media than 1.6.0 is available: 1.7.0 [GradleDependency]
    implementation("androidx.media:media:1.6.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:57: Warning: A newer version of com.google.guava:guava than 31.1-android is available: 33.3.1-android [GradleDependency]
    implementation("com.google.guava:guava:31.1-android")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:60: Warning: A newer version of com.squareup.retrofit2:retrofit than 2.9.0 is available: 3.0.0 [GradleDependency]
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:61: Warning: A newer version of com.squareup.retrofit2:converter-gson than 2.9.0 is available: 3.0.0 [GradleDependency]
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:71: Warning: A newer version of androidx.recyclerview:recyclerview than 1.3.2 is available: 1.4.0 [GradleDependency]
    implementation("androidx.recyclerview:recyclerview:1.3.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:77: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.2 [GradleDependency]
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:78: Warning: A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.2 [GradleDependency]
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:81: Warning: A newer version of org.jetbrains.kotlinx:kotlinx-coroutines-android than 1.7.3 is available: 1.8.1 [GradleDependency]
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\gradle\libs.versions.toml:3: Warning: A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.2.0 [GradleDependency]
kotlin = "2.0.21"
         ~~~~~~~~
D:\Android Projects\MusicPlayer\gradle\libs.versions.toml:8: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
appcompat = "1.6.1"
            ~~~~~~~
D:\Android Projects\MusicPlayer\gradle\libs.versions.toml:9: Warning: A newer version of com.google.android.material:material than 1.10.0 is available: 1.12.0 [GradleDependency]
material = "1.10.0"
           ~~~~~~~~
D:\Android Projects\MusicPlayer\gradle\libs.versions.toml:11: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:40: Warning: Expecting android:screenOrientation="unspecified" or "fullSensor" for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices [LockedOrientationActivity]
            android:screenOrientation="portrait" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "LockedOrientationActivity":
   The <activity> element should not be locked to any orientation so that
   users can take advantage of the multi-window environments and larger
   screens available on Android. To fix the issue, consider declaring the
   corresponding activity element with `screenOrientation="unspecified"`or
   fullSensor attribute.

   https://developer.android.com/topic/arc/window-management

D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:40: Warning: Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed. [DiscouragedApi]
            android:screenOrientation="portrait" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DiscouragedApi":
   Discouraged APIs are allowed and are not deprecated, but they may be unfit
   for common use (e.g. due to slow performance or subtle behavior).

D:\Android Projects\MusicPlayer\app\src\main\res\layout\item_song.xml:75: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
        android:tint="@color/colorPrimary" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\mini_player.xml:90: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="@color/text_primary" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\mini_player.xml:100: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="@color/text_primary" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\mini_player.xml:109: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
                android:tint="@color/text_primary" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseAppTint":
   ImageView or ImageButton uses android:tint instead of app:tint

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

D:\Android Projects\MusicPlayer\app\src\main\java\com\nauh\musicplayer\ui\adapter\SongAdapter.kt:55: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged()
        ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotifyDataSetChanged":
   The RecyclerView adapter's onNotifyDataSetChanged method does not specify
   what about the data set has changed, forcing any observers to assume that
   all existing items and structure may no longer be valid. `LayoutManager`s
   will be forced to fully rebind and relayout all visible views.

D:\Android Projects\MusicPlayer\app\src\main\res\layout\activity_player.xml:7: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Theme.MusicPlayer) [Overdraw]
    android:background="@android:color/white"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\item_song.xml:9: Warning: Possible overdraw: Root element paints background ?android:attr/selectableItemBackground with a theme that also paints a background (inferred theme is @style/Theme.MusicPlayer) [Overdraw]
    android:background="?android:attr/selectableItemBackground"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\mini_player.xml:8: Warning: Possible overdraw: Root element paints background @color/surface with a theme that also paints a background (inferred theme is @style/Theme.MusicPlayer) [Overdraw]
    android:background="@color/surface"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.primary_light appears to be unused [UnusedResources]
    <color name="primary_light">#F48FB1</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:14: Warning: The resource R.color.headphone_pink appears to be unused [UnusedResources]
    <color name="headphone_pink">#E91E63</color>
           ~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:15: Warning: The resource R.color.headphone_red appears to be unused [UnusedResources]
    <color name="headphone_red">#F44336</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:19: Warning: The resource R.color.background_secondary appears to be unused [UnusedResources]
    <color name="background_secondary">#1E1E1E</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:21: Warning: The resource R.color.surface_variant appears to be unused [UnusedResources]
    <color name="surface_variant">#3C3C3C</color>
           ~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:25: Warning: The resource R.color.text_secondary appears to be unused [UnusedResources]
    <color name="text_secondary">#B3B3B3</color>
           ~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:26: Warning: The resource R.color.text_tertiary appears to be unused [UnusedResources]
    <color name="text_tertiary">#808080</color>
           ~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:29: Warning: The resource R.color.progress_background appears to be unused [UnusedResources]
    <color name="progress_background">#404040</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:32: Warning: The resource R.color.accent appears to be unused [UnusedResources]
    <color name="accent">#FF6B6B</color>
           ~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:33: Warning: The resource R.color.error appears to be unused [UnusedResources]
    <color name="error">#F44336</color>
           ~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:34: Warning: The resource R.color.success appears to be unused [UnusedResources]
    <color name="success">#4CAF50</color>
           ~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml:35: Warning: The resource R.color.warning appears to be unused [UnusedResources]
    <color name="warning">#FF9800</color>
           ~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_equalizer.xml:1: Warning: The resource R.drawable.ic_equalizer appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_more_vert.xml:1: Warning: The resource R.drawable.ic_more_vert appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_play_arrow.xml:1: Warning: The resource R.drawable.ic_play_arrow appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_search.xml:1: Warning: The resource R.drawable.ic_search appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Android Projects\MusicPlayer\app\src\main\res\layout\mini_player.xml:2: Warning: The resource R.layout.mini_player appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:5: Warning: The resource R.string.search_hint appears to be unused [UnusedResources]
    <string name="search_hint">Search songs, artists, albums…</string>
            ~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:6: Warning: The resource R.string.no_songs_found appears to be unused [UnusedResources]
    <string name="no_songs_found">No songs found</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:7: Warning: The resource R.string.try_refreshing appears to be unused [UnusedResources]
    <string name="try_refreshing">Try refreshing or check your connection</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:10: Warning: The resource R.string.now_playing appears to be unused [UnusedResources]
    <string name="now_playing">Now Playing</string>
            ~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:11: Warning: The resource R.string.play appears to be unused [UnusedResources]
    <string name="play">Play</string>
            ~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:12: Warning: The resource R.string.pause appears to be unused [UnusedResources]
    <string name="pause">Pause</string>
            ~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:13: Warning: The resource R.string.previous appears to be unused [UnusedResources]
    <string name="previous">Previous</string>
            ~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:14: Warning: The resource R.string.next appears to be unused [UnusedResources]
    <string name="next">Next</string>
            ~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:15: Warning: The resource R.string.shuffle appears to be unused [UnusedResources]
    <string name="shuffle">Shuffle</string>
            ~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:16: Warning: The resource R.string.repeat appears to be unused [UnusedResources]
    <string name="repeat">Repeat</string>
            ~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:17: Warning: The resource R.string.more_options appears to be unused [UnusedResources]
    <string name="more_options">More options</string>
            ~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:20: Warning: The resource R.string.notification_channel_name appears to be unused [UnusedResources]
    <string name="notification_channel_name">Music Playback</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:21: Warning: The resource R.string.notification_channel_description appears to be unused [UnusedResources]
    <string name="notification_channel_description">Controls for music playback</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:24: Warning: The resource R.string.error_loading_songs appears to be unused [UnusedResources]
    <string name="error_loading_songs">Failed to load songs</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:25: Warning: The resource R.string.error_network appears to be unused [UnusedResources]
    <string name="error_network">Network error. Please check your connection.</string>
            ~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:26: Warning: The resource R.string.error_playback appears to be unused [UnusedResources]
    <string name="error_playback">Playback error occurred</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:29: Warning: The resource R.string.album_artwork appears to be unused [UnusedResources]
    <string name="album_artwork">Album artwork</string>
            ~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml:30: Warning: The resource R.string.playing_indicator appears to be unused [UnusedResources]
    <string name="playing_indicator">Currently playing</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

D:\Android Projects\MusicPlayer\app\src\main\res\layout\mini_player.xml:3: Warning: Unused namespace app [UnusedNamespace]
    xmlns:app="http://schemas.android.com/apk/res/auto"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedNamespace":
   Unused namespace declarations take up space and require processing that is
   not necessary

D:\Android Projects\MusicPlayer\app\src\main\java\com\nauh\musicplayer\service\MusicService.kt:40: Warning: Use the KTX extension function String.toUri instead? [UseKtx]
                .setArtworkUri(android.net.Uri.parse(song.artworkUrl))
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseKtx":
   The Android KTX libraries decorates the Android platform SDK as well as
   various libraries with more convenient extension functions available from
   Kotlin, allowing you to use default parameters, named parameters, and
   more.

   Available options:

   **remove-defaults** (default is true):
   Whether to skip arguments that match the defaults provided by the extension.

   Extensions often provide default values for some of the parameters. For example:
   ```kotlin
   fun Path.readLines(charset: Charset = Charsets.UTF_8): List<String> { return Files.readAllLines(this, charset) }
   ```
   This lint check will by default automatically omit parameters that match the default, so if your code was calling

   ```kotlin
   Files.readAllLines(file, Charset.UTF_8)
   ```
   lint would replace this with
   ```kotlin
   file.readLines()
   ```
   rather than

   ```kotlin
   file.readLines(Charset.UTF_8
   ```
   You can turn this behavior off using this option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="remove-defaults" value="true" />
       </issue>
   </lint>
   ```

   **require-present** (default is true):
   Whether to only offer extensions already available.

   This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="require-present" value="true" />
       </issue>
   </lint>
   ```

D:\Android Projects\MusicPlayer\app\build.gradle.kts:48: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.media3:media3-exoplayer:1.2.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:49: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.media3:media3-ui:1.2.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:50: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.media3:media3-session:1.2.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:51: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.media3:media3-common:1.2.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:54: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.media:media:1.6.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:57: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.guava:guava:31.1-android")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:60: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:61: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:62: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:65: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.github.bumptech.glide:glide:4.16.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:68: Warning: Use version catalog instead [UseTomlInstead]
    implementation("de.hdodenhof:circleimageview:3.1.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:71: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.recyclerview:recyclerview:1.3.2")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:74: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.1.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:77: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:78: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\build.gradle.kts:81: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

D:\Android Projects\MusicPlayer\app\src\main\res\layout\activity_player.xml:36: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\item_song.xml:15: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\item_song.xml:68: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\mini_player.xml:33: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

D:\Android Projects\MusicPlayer\app\src\main\res\layout\activity_main.xml:80: Warning: Hardcoded string "No songs found", should use @string resource [HardcodedText]
            android:text="No songs found"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\activity_player.xml:18: Warning: Hardcoded string "Back", should use @string resource [HardcodedText]
        android:contentDescription="Back"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\activity_player.xml:156: Warning: Hardcoded string "Shuffle", should use @string resource [HardcodedText]
            android:contentDescription="Shuffle" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\activity_player.xml:165: Warning: Hardcoded string "Previous", should use @string resource [HardcodedText]
            android:contentDescription="Previous" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\activity_player.xml:180: Warning: Hardcoded string "Play", should use @string resource [HardcodedText]
                android:contentDescription="Play" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\activity_player.xml:191: Warning: Hardcoded string "Next", should use @string resource [HardcodedText]
            android:contentDescription="Next" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Android Projects\MusicPlayer\app\src\main\res\layout\activity_player.xml:199: Warning: Hardcoded string "Repeat", should use @string resource [HardcodedText]
            android:contentDescription="Repeat" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

7 errors, 93 warnings
