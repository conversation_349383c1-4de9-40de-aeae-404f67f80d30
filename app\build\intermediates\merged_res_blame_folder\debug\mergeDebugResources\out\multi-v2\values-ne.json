{"logs": [{"outputFile": "com.nauh.musicplayer.app-mergeDebugResources-40:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87c47c7abe7f29a54b096c7ff4d96851\\transformed\\core-1.16.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "56,57,58,59,60,61,62,185", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4268,4371,4474,4576,4682,4780,4880,14669", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "4366,4469,4571,4677,4775,4875,4983,14765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8c6e034149c65399529a9cd0f184f8b\\transformed\\media3-session-1.2.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,132,213,288,364,445,529,621", "endColumns": "76,80,74,75,80,83,91,97", "endOffsets": "127,208,283,359,440,524,616,714"}, "to": {"startLines": "50,66,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3748,5287,10532,10607,10683,10764,10848,10940", "endColumns": "76,80,74,75,80,83,91,97", "endOffsets": "3820,5363,10602,10678,10759,10843,10935,11033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e355fbe8fe59cc5407000c654ab4b66\\transformed\\appcompat-1.6.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,2865", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,2940"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "988,1097,1208,1316,1407,1514,1634,1718,1797,1888,1981,2076,2170,2270,2363,2458,2552,2643,2734,2820,2933,3034,3130,3243,3353,3470,3637,14589", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "1092,1203,1311,1402,1509,1629,1713,1792,1883,1976,2071,2165,2265,2358,2453,2547,2638,2729,2815,2928,3029,3125,3238,3348,3465,3632,3743,14664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c6ed593d095e40b444f65ff3ae72d38a\\transformed\\material-1.10.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1070,1166,1232,1293,1398,1462,1534,1592,1666,1728,1782,1895,1955,2016,2075,2153,2277,2358,2443,2579,2660,2743,2874,2957,3043,3105,3159,3225,3302,3381,3469,3552,3621,3697,3778,3846,3950,4041,4119,4212,4309,4383,4462,4560,4620,4708,4774,4862,4950,5012,5080,5143,5209,5314,5420,5515,5620,5686,5744", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,88,87,81,94,88,101,109,86,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,130,82,85,61,53,65,76,78,87,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83", "endOffsets": "257,346,434,516,611,700,802,912,999,1065,1161,1227,1288,1393,1457,1529,1587,1661,1723,1777,1890,1950,2011,2070,2148,2272,2353,2438,2574,2655,2738,2869,2952,3038,3100,3154,3220,3297,3376,3464,3547,3616,3692,3773,3841,3945,4036,4114,4207,4304,4378,4457,4555,4615,4703,4769,4857,4945,5007,5075,5138,5204,5309,5415,5510,5615,5681,5739,5823"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,67,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "826,3825,3914,4002,4084,4179,4988,5090,5200,5368,9325,9421,9487,9548,9653,9717,9789,9847,9921,9983,10037,10150,10210,10271,10330,10408,11038,11119,11204,11340,11421,11504,11635,11718,11804,11866,11920,11986,12063,12142,12230,12313,12382,12458,12539,12607,12711,12802,12880,12973,13070,13144,13223,13321,13381,13469,13535,13623,13711,13773,13841,13904,13970,14075,14181,14276,14381,14447,14505", "endLines": "22,51,52,53,54,55,63,64,65,67,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "endColumns": "12,88,87,81,94,88,101,109,86,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,130,82,85,61,53,65,76,78,87,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83", "endOffsets": "983,3909,3997,4079,4174,4263,5085,5195,5282,5429,9416,9482,9543,9648,9712,9784,9842,9916,9978,10032,10145,10205,10266,10325,10403,10527,11114,11199,11335,11416,11499,11630,11713,11799,11861,11915,11981,12058,12137,12225,12308,12377,12453,12534,12602,12706,12797,12875,12968,13065,13139,13218,13316,13376,13464,13530,13618,13706,13768,13836,13899,13965,14070,14176,14271,14376,14442,14500,14584"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c077dfbc8dc20562e970eb684d92a281\\transformed\\media3-ui-1.2.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,310,529,731,824,917,1007,1108,1211,1297,1361,1456,1551,1623,1696,1756,1826,1943,2057,2177,2256,2348,2416,2502,2588,2673,2742,2805,2858,2916,2964,3025,3087,3158,3220,3282,3341,3408,3474,3537,3604,3658,3720,3796,3872", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,92,92,89,100,102,85,63,94,94,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,62,66,53,61,75,75,52", "endOffsets": "305,524,726,819,912,1002,1103,1206,1292,1356,1451,1546,1618,1691,1751,1821,1938,2052,2172,2251,2343,2411,2497,2583,2668,2737,2800,2853,2911,2959,3020,3082,3153,3215,3277,3336,3403,3469,3532,3599,3653,3715,3791,3867,3920"}, "to": {"startLines": "2,11,15,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,405,624,5434,5527,5620,5710,5811,5914,6000,6064,6159,6254,6326,6399,6459,6529,6646,6760,6880,6959,7051,7119,7205,7291,7376,7445,8205,8258,8316,8364,8425,8487,8558,8620,8682,8741,8808,8874,8937,9004,9058,9120,9196,9272", "endLines": "10,14,18,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "endColumns": "17,12,12,92,92,89,100,102,85,63,94,94,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,62,66,53,61,75,75,52", "endOffsets": "400,619,821,5522,5615,5705,5806,5909,5995,6059,6154,6249,6321,6394,6454,6524,6641,6755,6875,6954,7046,7114,7200,7286,7371,7440,7503,8253,8311,8359,8420,8482,8553,8615,8677,8736,8803,8869,8932,8999,9053,9115,9191,9267,9320"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d41d36b14e47c257e85459df73d97ebd\\transformed\\media3-exoplayer-1.2.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,197,267,334,412,489,589,683", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "121,192,262,329,407,484,584,678,747"}, "to": {"startLines": "92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7508,7579,7650,7720,7787,7865,7942,8042,8136", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "7574,7645,7715,7782,7860,7937,8037,8131,8200"}}]}]}